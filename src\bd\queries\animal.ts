import database from "../";
import { Animal } from "@/bd/model";
import { Table } from "../enum/table";

export const animalCollection = database.get<Animal>(Table.animals);

export const onAdd = async (animalToAdd: Animal) => {
  await database.write(async () => {
    await animalCollection.create((animal) => {
      for (const key in animal) {
        animal[key] = animalToAdd[key];
      }
    });
  });
};

export const onView = async () => {
  //console.log("Viewing  Main");
  console.log(Animal.table);
  const posts = await animalCollection.query().fetch();
  console.log(posts);
};

import React from 'react';
import { TextInput as RNTextInput, TextInputProps } from 'react-native';
import { useTheme } from '@/context/ThemeContext';
import { cn } from '@/lib/utils';

type AppTextInputProps = TextInputProps & {
  className?: string;
};

const AppTextInput = React.forwardRef<RNTextInput, AppTextInputProps>(
  (
    {
      className = '',
      editable = true, // Add default value
      ...props
    },
    ref
  ) => {
    const { currentTheme } = useTheme();
    const isDark = currentTheme === 'dark';

    const textColor = isDark ? 'text-white' : 'text-gray-900';

    return (
      <RNTextInput
        ref={ref}
        className={cn(
          textColor,
          className,
          !editable && 'opacity-60' // Add reduced opacity when not editable
        )}
        editable={editable}
        placeholderTextColor={isDark ? '#6B7280' : '#9CA3AF'}
        {...props}
      />
    );
  }
);

// Add display name for better debugging
AppTextInput.displayName = 'AppTextInput';

export default AppTextInput;

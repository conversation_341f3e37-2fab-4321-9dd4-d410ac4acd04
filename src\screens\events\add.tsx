import { AppTextInput, AppView } from "@/components/common";
import {
  Form,
  FormDatePicker,
  FormField,
  SubmitButton,
} from "@/components/form";
import z from "zod";

const schema = z.object({
  title: z.string().min(3, `Title must be at least ${3} characters`),
  description: z
    .string()
    .min(3, `Description must be at least ${3} characters`),
  date: z.date(),
  time: z.date(),
  icon: z.string().optional(),
  iconColor: z.string().optional(),
  bgColor: z.string().optional(),
});

type AddEventValues = z.infer<typeof schema>;

export default function AddEvent() {
  return (
    <AppView>
      <Form<AddEventValues, typeof schema>
        schema={schema}
        onSubmit={(values) => console.log(values)}
      >
        <FormField name="title" label="Title" />
        <FormDatePicker mode="date" name="date" label="Date" />
        <FormDatePicker
          mode="time"
          placeholder="Select Time"
          name="time"
          label="Time"
        />
        <FormField
          name="description"
          label="Description"
          numberOfLines={3}
          multiline
        />
        <SubmitButton label="Save" />
      </Form>
    </AppView>
  );
}

{"name": "farm-track", "version": "1.0.9", "main": "expo-router/entry", "scripts": {"start": "expo start --dev-client", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "deploy": "npx expo export -p web && npx eas-cli@latest deploy", "eas-android-dev-build": "eas build --platform android --profile development"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@nozbe/watermelondb": "^0.28.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "8.3.0", "@shopify/flash-list": "^2.0.0-rc.5", "clsx": "^2.1.1", "expo": "53.0.11", "expo-build-properties": "~0.14.6", "expo-constants": "~17.1.4", "expo-dev-client": "~5.2.0", "expo-image-manipulator": "~13.1.7", "expo-image-picker": "~16.1.4", "expo-linking": "~7.1.4", "expo-router": "~5.0.3", "expo-splash-screen": "~0.30.7", "expo-status-bar": "~2.2.3", "nativewind": "^4.0.1", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.57.0", "react-native": "0.79.3", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-web": "^0.20.0", "reanimated-color-picker": "^4.1.0", "tailwind-merge": "^3.3.0", "tailwindcss": "^3.4.0", "zod": "^3.25.63", "react-native-gesture-handler": "~2.24.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-decorators": "^7.27.1", "@babel/plugin-transform-runtime": "^7.27.4", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}
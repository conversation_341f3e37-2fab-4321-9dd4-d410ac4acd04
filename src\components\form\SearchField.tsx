import FormField from "@/components/form/FormField";
import React, {
  ComponentProps,
  forwardRef,
  useCallback,
  useState,
} from "react";
import { FieldValues } from "react-hook-form";
import { TextInput, TouchableOpacity } from "react-native";
import { AppText, AppView } from "../common";

type SearchFieldProps = ComponentProps<typeof FormField>;

interface SearchProps<T extends FieldValues> extends SearchFieldProps {
  name: string;
  placeholder?: string;
  onSearch?: (value: string) => void;
  onClear?: () => void;
  onPress?: () => void;
  onChangeText?: (value: string) => void;
  containerClass?: string;
  inputClass?: string;
  editable?: boolean;
  value?: string; // Make sure value prop is defined
}

const SearchField = forwardRef<TextInput, SearchProps<FieldValues>>(
  (
    {
      name,
      placeholder = "Search...",
      onSearch,
      onClear,
      onPress,
      onChangeText,
      containerClass,
      inputClass,
      editable = true,
      value = "", // Provide default value
      ...props
    },
    ref
  ) => {
    const [error, setError] = useState<string | null>(null);

    // Handle immediate UI update only
    const handleChange = useCallback(
      (text: string) => {
        setError(null); // Clear error when typing
        onChangeText?.(text); // Pass the change up to parent
      },
      [onChangeText]
    );

    const handleClear = useCallback(() => {
      onClear?.();
      setError(null);
    }, [onClear]);

    const handlePress = useCallback(() => {
      onPress?.();
    }, [onPress]);

    return (
      <AppView>
        <FormField<T>
          ref={ref}
          name={name}
          value={value} // Use the value from props
          onChangeText={handleChange}
          placeholder={placeholder}
          containerClass={containerClass}
          inputClass={inputClass}
          editable={editable}
          onPress={handlePress}
          LeftIcon={<SearchIcon size={20} color="#9CA3AF" />}
          RightIcon={
            value ? (
              <TouchableOpacity onPress={handleClear}>
                <CloseIcon size={20} color="#9CA3AF" />
              </TouchableOpacity>
            ) : undefined
          }
          error={error}
          {...props}
        />
        {error && (
          <AppText className="text-red-500 text-sm mt-1 ml-2">{error}</AppText>
        )}
      </AppView>
    );
  }
);

export default SearchField;

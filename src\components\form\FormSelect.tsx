import { useTheme } from "@/context/ThemeContext";
import { useThemeColor } from "@/hooks/useThemeColor";
import { cn } from "@/lib/utils";
import { HideCondition } from "@/lib/types";
import { Ionicons } from "@expo/vector-icons";
import React, { useState, useEffect } from "react";
import {
  FieldValues,
  useController,
  UseControllerProps,
  useFormContext,
} from "react-hook-form";
import { Modal, Platform, TouchableOpacity } from "react-native";
import { AppText, AppView } from "../common";

export type SelectOption = {
  label: string;
  value: string | number;
};

type FormSelectProps<T extends FieldValues> = UseControllerProps<T> & {
  label?: string;
  placeholder?: string;
  options: SelectOption[];
  containerClass?: string;
  labelClass?: string;
  selectClass?: string;
  errorClass?: string;
  hide?: HideCondition; // Use the centralized HideCondition type
};

export default function FormSelect<T extends FieldValues>({
  name,
  label,
  placeholder = "Select an option",
  options,
  control,
  containerClass,
  labelClass,
  selectClass,
  errorClass,
  hide, // Add hide prop
}: FormSelectProps<T>) {
  const [modalVisible, setModalVisible] = useState(false);
  const form = useFormContext();
  const { field, fieldState } = useController({
    control: control ?? form.control,
    name,
  });
  const primaryColor = useThemeColor("primary");
  const { currentTheme } = useTheme();
  const isDark = currentTheme === "dark";

  // Check if the field should be hidden based on another field's value
  const shouldHide = hide
    ? form.watch(hide.dependsOn) === hide.hideValue
    : false;

  // Clear the field value when hidden if clearOnHide is true
  useEffect(() => {
    if (shouldHide && hide?.clearOnHide && field.value) {
      field.onChange(undefined);
    }
  }, [shouldHide, hide, field]);

  if (shouldHide) {
    return null; // Don't render anything if hide condition is met
  }

  const selectedOption = field.value
    ? options.find((opt) => opt.value === field.value)
    : undefined;

  const handleSelect = (value: string | number) => {
    field.onChange(value);
    setModalVisible(false);
  };

  const renderMobileSelect = () => (
    <>
      <TouchableOpacity
        onPress={() => setModalVisible(true)}
        className={cn(
          "flex-row justify-between items-center p-3 border border-gray-300 rounded-lg",
          fieldState.error && "border-red-500",
          selectClass
        )}
      >
        <AppText className={cn(!selectedOption && "text-gray-500")}>
          {selectedOption ? selectedOption.label : placeholder}
        </AppText>
        <Ionicons name="chevron-down" size={20} color="gray" />
      </TouchableOpacity>

      <Modal
        visible={modalVisible}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setModalVisible(false)}
      >
        <TouchableOpacity
          activeOpacity={1}
          onPress={() => setModalVisible(false)}
          className="flex-1 bg-black/50"
        >
          <AppView className="flex-1 justify-end">
            <TouchableOpacity activeOpacity={1}>
              <AppView variant="card" className="rounded-t-xl">
                <AppView className="flex-row justify-between items-center p-4 border-b border-gray-200">
                  <AppText variant="h3">{label || "Select"}</AppText>
                  <TouchableOpacity
                    onPress={() => setModalVisible(false)}
                    className="flex-row items-center"
                  >
                    <AppText className="text-gray-400 mr-1">Done</AppText>
                  </TouchableOpacity>
                </AppView>

                <AppView className={cn("max-h-[50%]", "mb-4 overflow-y-auto")}>
                  {options.map((option, index) => (
                    <TouchableOpacity
                      key={option.value}
                      onPress={() => handleSelect(option.value)}
                      className={cn(
                        "flex-row justify-between items-center p-4",
                        isDark
                          ? "bg-gray-800 border-gray-700"
                          : "bg-white border-gray-200",
                        index !== options.length - 1 &&
                          "border-b border-gray-200",
                        option.value === field.value && "bg-primary/10"
                      )}
                    >
                      <AppText
                        className={cn(
                          option.value === field.value &&
                            "text-primary font-medium"
                        )}
                      >
                        {option.label}
                      </AppText>
                      {option.value === field.value && (
                        <Ionicons
                          name="checkmark"
                          size={20}
                          color={primaryColor}
                        />
                      )}
                    </TouchableOpacity>
                  ))}
                </AppView>
              </AppView>
            </TouchableOpacity>
          </AppView>
        </TouchableOpacity>
      </Modal>
    </>
  );

  const renderWebSelect = () => (
    <AppView
      className={cn(
        "relative border border-gray-300 rounded-lg overflow-hidden ",
        fieldState.error && "border-red-500",
        selectClass
      )}
    >
      <select
        value={field.value ?? ""}
        onChange={(e) => field.onChange(e.target.value)}
        className={`w-full p-3 appearance-none bg-black/0 ${
          currentTheme === "dark" ? "text-white" : "text-black"
        }
 `}
      >
        <option value="" disabled>
          {placeholder}
        </option>
        {options.map((option) => (
          <option
            className={`${currentTheme === "dark" ? "text-black" : ""}`}
            key={option.value}
            value={option.value}
          >
            {option.label}
          </option>
        ))}
      </select>
      <AppView className="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
        <Ionicons name="chevron-down" size={20} color="gray" />
      </AppView>
    </AppView>
  );

  return (
    <AppView className={cn("mb-4", containerClass)}>
      {label && (
        <AppText variant="label" className={cn("mb-1", labelClass)}>
          {label}
        </AppText>
      )}

      {Platform.OS === "web" ? renderWebSelect() : renderMobileSelect()}

      {fieldState.error && (
        <AppText className={cn("text-red-500 text-sm mt-1", errorClass)}>
          {fieldState.error.message}
        </AppText>
      )}
    </AppView>
  );
}

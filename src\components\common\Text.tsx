import { Text as RNText, TextProps } from 'react-native';
import { useTheme } from '@/context/ThemeContext';
import { cn } from '@/lib/utils';

const variants = {
  h1: 'text-3xl font-bold',
  h2: 'text-2xl font-bold',
  h3: 'text-xl font-bold',
  body: 'text-base',
  button: 'font-bold text-lg',
  price: 'text-primary text-2xl font-bold',
  caption: 'text-sm text-gray-500',
  label: 'text-sm font-medium',
} as const;

type Variant = keyof typeof variants;

type AppTextProps = TextProps & {
  variant?: Variant;
  className?: string;
  children: React.ReactNode;
};

export default function AppText({
  children,
  variant = 'body',
  className = '',
  ...props
}: AppTextProps) {
  const { currentTheme } = useTheme();
  const isDark = currentTheme === 'dark';

  // Base text color based on theme
  const baseTextColor = isDark ? 'text-white' : 'text-gray-900';
  
  // Special handling for caption variant
  const captionColor = isDark ? 'text-gray-400' : 'text-gray-500';
  const variantWithTheme = variant === 'caption' 
    ? variants[variant].replace('text-gray-500', captionColor)
    : variants[variant];

  return (
    <RNText
      className={cn(baseTextColor, variantWithTheme, className)}
      {...props}
    >
      {children}
    </RNText>
  );
}
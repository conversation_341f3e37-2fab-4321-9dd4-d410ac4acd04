{"expo": {"name": "Farm Track", "slug": "farm-track", "version": "1.0.9", "scheme": "farmtrack", "userInterfaceStyle": "automatic", "orientation": "default", "web": {"output": "static"}, "plugins": [["expo-router", {"origin": "https://n"}], ["expo-image-picker", {"photosPermission": "Allow access to your photos to select images", "cameraPermission": "Allow access to your camera to take photos"}], ["expo-build-properties", {"android": {"package": "com.suluuboi.farmtrack"}, "ios": {"bundleIdentifier": "com.suluuboi.farmtrack", "extraPods": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "configurations": ["Debug", "Release"], "path": "../node_modules/@nozbe/simdjson", "modular_headers": true}]}}]], "extra": {"router": {"origin": "https://n"}, "eas": {"projectId": "659200ad-43c0-45af-9b41-fe7aa99b4adf"}}, "owner": "su<PERSON><PERSON><PERSON>", "android": {"package": "com.suluuboi.farmtrack"}}}
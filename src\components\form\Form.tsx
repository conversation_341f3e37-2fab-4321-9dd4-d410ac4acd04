/** Use this reusable component to wrap around a form to ensure no need for complext loging when creating a form */

import React, { createContext, useContext } from 'react';
import { AppScrollView as ScrollView } from '@/components/common';
import {
  FormProvider,
  useForm,
  FieldValues,
  SubmitHandler,
  DefaultValues,
} from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { ZodSchema, TypeOf } from 'zod';

// Create context for submit handler
const SubmitHandlerContext = createContext<SubmitHandler<any>>(() => {});
export const useSubmitHandler = () => useContext(SubmitHandlerContext);

type FormProps<TFormValues extends FieldValues, Schema> = {
  schema: Schema;
  onSubmit: SubmitHandler<TFormValues>;
  children: React.ReactNode;
  className?: string;
  initialValues?: Partial<TFormValues>;
};

export default function Form<
  TFormValues extends FieldValues,
  Schema extends ZodSchema<TFormValues>
>({
  schema,
  onSubmit,
  children,
  className,
  initialValues,
}: FormProps<TFormValues, Schema>) {
  const methods = useForm<TFormValues>({
    resolver: zodResolver(schema),
    mode: 'onTouched',
    defaultValues: initialValues as DefaultValues<TFormValues>,
  });

  return (
    <ScrollView className={className}>
      <SubmitHandlerContext.Provider value={onSubmit}>
        <FormProvider {...methods}>{children}</FormProvider>
      </SubmitHandlerContext.Provider>
    </ScrollView>
  );
}

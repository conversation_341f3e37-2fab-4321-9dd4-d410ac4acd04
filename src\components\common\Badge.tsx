import AppText from "./Text";
import AppView from "./View";

interface Props {
  children: string;
  labelColor?: string;
}

export default function Badge({ children, labelColor }: Props) {
  return (
    <AppView
      className="absolute top-2 right-2 rounded-full px-2 py-0.5 border shadow"
      style={{
        backgroundColor: `${labelColor}66`, // ~60% opacity
        borderColor: labelColor,
        shadowColor: "#000",
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.25,
        shadowRadius: 2,
        elevation: 3, // Android shadow
      }}
    >
      <AppText
        className="text-xs font-bold"
        style={{
          color: labelColor, // Use white text for high contrast
          textShadowColor: "#00000080", // dark shadow
          textShadowOffset: { width: 0.5, height: 0.5 },
          textShadowRadius: 1.5,
        }}
      >
        {children}
      </AppText>
    </AppView>
  );
}

// Alpha (Hex) → Opacity (%)
// FF = 100%
// E6 = 90%
// CC = 80%
// B3 = 70%
// 99 = 60%
// 80 = 50%
// 66 = 40%
// 4D = 30%
// 33 = 20%
// 1A = 10%
// 00 = 0%

import { View as RNView, ViewProps } from "react-native";
import { useTheme } from "@/context/ThemeContext";
import { cn } from "@/lib/utils";

type AppViewProps = ViewProps & {
  className?: string;
  children?: React.ReactNode;
  variant?:
    | "card"
    | "borderdCard"
    | "container"
    | "section"
    | "default"
    | "page";
};

export default function AppView({
  children,
  className = "",
  variant = "default",
  ...props
}: AppViewProps) {
  const { currentTheme } = useTheme();
  const isDark = currentTheme === "dark";

  const variants = {
    card: isDark ? "bg-gray-800 border-gray-700" : "bg-white border-gray-200",
    borderdCard: isDark
      ? "bg-gray-900 border border-gray-700"
      : "transparent border border-gray-300",
    container: isDark ? "bg-gray-900" : "bg-gray-50",
    section: isDark
      ? "bg-gray-800 border-gray-700"
      : "bg-white border-gray-200",
    default: "",
    page: `flex-1 ${isDark ? "bg-gray-900" : "bg-gray-50"}`,
  };

  return (
    <RNView className={cn(variants[variant], className)} {...props}>
      {children}
    </RNView>
  );
}

// components/form/FormImagePicker.tsx
import React from 'react';
import {AppView as View, AppText as Text } from '@/components/common';
import {
  FieldValues,
  useController,
  UseControllerProps,
  useFormContext,
} from 'react-hook-form';
import AdvancedImagePicker, {
  ImagePickerProps,
} from '@/components/image/Picker';
import { cn } from '@/lib/utils';

type FormImagePickerProps<T extends FieldValues> = ImagePickerProps &
  UseControllerProps<T> & {
    label?: string;
    containerClass?: string;
    labelClass?: string;
    errorClass?: string;
  };

export default function FormImagePicker<T extends FieldValues>({
  name,
  control,
  label,
  containerClass,
  labelClass,
  errorClass,
  ...props
}: FormImagePickerProps<T>) {
  const form = useFormContext<T>();
  const { field, fieldState } = useController({
    control: control ?? form.control,
    name,
  });

  return (
    <View className={cn('mb-4', containerClass)}>
      {label && (
        <Text className={cn('text-sm mb-1', labelClass)}>
          {label}
        </Text>
      )}

      <AdvancedImagePicker
        {...props}
        selectedImages={field.value}
        onImagesChange={field.onChange}
      />

      {fieldState.error && (
        <Text className={cn('text-red-500 text-sm mt-1', errorClass)}>
          {fieldState.error.message}
        </Text>
      )}
    </View>
  );
}

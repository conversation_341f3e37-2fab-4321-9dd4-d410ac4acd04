import { cn } from '@/lib/utils';
import React from 'react';
import { Image, ImageProps } from 'react-native';
import AppView from './View';
import AppText from './Text';

interface AvatarProps {
  imageUrl?: string | null;
  firstName?: string | null;
  lastName?: string | null;
  size?: number;
  className?: string;
  textClassName?: string;
  backgroundColor?: string;
  imageProps?: Partial<ImageProps>;
}

export function Avatar({
  imageUrl,
  firstName,
  lastName,
  size = 80, // default size of 80 (equivalent to w-20/h-20)
  className,
  textClassName,
  backgroundColor = 'bg-primary',
  imageProps,
}: AvatarProps) {
  const initials = `${firstName?.[0] || ''}${
    lastName?.[0] || ''
  }`.toUpperCase();
  const dimensionStyle = { width: size, height: size };

  if (imageUrl) {
    return (
      <Image
        source={{ uri: imageUrl }}
        className={cn('rounded-full', className)}
        style={[dimensionStyle, imageProps?.style]}
        {...imageProps}
      />
    );
  }

  return (
    <AppView
      className={cn(
        'rounded-full items-center justify-center',
        backgroundColor,
        className
      )}
      style={dimensionStyle}
    >
      <AppText
        className={cn('font-bold text-white', getFontSize(size), textClassName)}
      >
        {initials}
      </AppText>
    </AppView>
  );
}

// Helper function to determine appropriate font size based on avatar size
function getFontSize(size: number): string {
  if (size <= 32) return 'text-sm'; // small
  if (size <= 48) return 'text-lg'; // medium
  if (size <= 80) return 'text-2xl'; // large
  return 'text-3xl'; // extra large
}

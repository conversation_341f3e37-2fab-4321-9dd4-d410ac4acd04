// EventsScreen.tsx
import React from "react";
import { ScrollView, TouchableOpacity, View } from "react-native";
import { MaterialIcons } from "@expo/vector-icons";
import { AppView, AppText, AppScrollView } from "@/components/common";
import { allevents } from "@/data/event";
import { EventCard } from "@/components/event";
import { useThemeColor } from "@/hooks/useThemeColor";

const EventsScreen = () => {
  const primaryColor = useThemeColor("primary");
  return (
    <AppView variant="page" className="flex-1">
      <AppScrollView className="flex-1 ">
        <AppView className="px-6 py-4">
          {/* Filter Buttons */}
          <AppView className="flex-row space-x-2 mb-4">
            <AppView className="flex-1 flex-row space-x-2">
              <TouchableOpacity
                className={`flex-1 bg-primary py-2  rounded-lg`}
              >
                <AppText className="text-white text-sm font-medium text-center">
                  Upcoming
                </AppText>
              </TouchableOpacity>
              <TouchableOpacity className="flex-1 bg-gray-200 py-2 px-4 rounded-lg ml-2">
                <AppText className="text-gray-700 text-sm font-medium text-center">
                  Past
                </AppText>
              </TouchableOpacity>
            </AppView>
            <TouchableOpacity className="bg-gray-200 py-2 px-3 rounded-lg flex-row items-center ml-6">
              <MaterialIcons name="sort" size={16} color="#4B5563" />
              <AppText className="text-gray-700 text-sm ml-1">Sort</AppText>
            </TouchableOpacity>
          </AppView>

          {/* Sections */}
          {allevents.map((section, i) => (
            <AppView key={i} className="mb-6 ">
              <AppView className="flex-row items-center">
                <AppText className="text-ml font-medium text-gray-400 mb-1">
                  {section.date}
                </AppText>
                <AppView className="h-px bg-gray-400 flex-1 mx-2 self-center" />
              </AppView>

              <AppView>
                {section.events.map((event, j) => (
                  <EventCard key={j} {...event} />
                ))}
              </AppView>
            </AppView>
          ))}
        </AppView>
      </AppScrollView>
    </AppView>
  );
};

export default EventsScreen;

import { TouchableOpacity } from "react-native";
import { AppText, AppView } from "../common";
import { MaterialIcons } from "@expo/vector-icons";
import { AnimalEvent } from "@/data";

export default function EventCard({
  icon,
  title,
  description,
  time,
  bgColor,
  date,
  iconColor,
}: AnimalEvent & { time: string }) {
  return (
    <AppView variant="borderdCard" className="rounded-lg p-4 mb-4">
      <AppView className="flex-row justify-between">
        <AppView className="flex-row items-start space-x-5">
          <AppView className="bg-indigo-100 p-2 rounded-full mx-2">
            <MaterialIcons
              name={icon}
              size={20}
              color={iconColor || "#636699"}
            />
          </AppView>
          <AppView>
            <AppText className="font-semibold">{title}</AppText>
            <AppText className="text-sm text-gray-400">{description}</AppText>
            {/* <AppView className={`${bgColor} p-2 rounded-full mr-3 self-center`}>
              <MaterialIcons name={icon} size={20} className={iconColor} />
            </AppView> */}
          </AppView>
        </AppView>
        <AppView className="text-right text-xs">
          <AppText className="text-gray-500">{date}</AppText>
          <AppText className="text-gray-500">{time}</AppText>
        </AppView>
      </AppView>
      <AppView className="mt-3 flex-row justify-between items-center">
        <AppView className="bg-indigo-100 px-2 py-1 rounded-full">
          <AppText className="text-indigo-600 text-xs font-semibold">
            Upcoming
          </AppText>
        </AppView>
        <TouchableOpacity className="flex-row items-center">
          <AppText className="text-sm font-medium text-primary-light">
            View Details
          </AppText>
          <MaterialIcons name="chevron-right" size={20} color="grey" />
        </TouchableOpacity>
      </AppView>
    </AppView>
  );
}

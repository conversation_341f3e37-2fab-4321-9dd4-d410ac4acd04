import { useTheme } from "@/context/ThemeContext";
import { useThemeColor } from "@/hooks/useThemeColor";
import { cn } from "@/lib/utils";
import { Ionicons } from "@expo/vector-icons";
import React, { useState, useEffect, useRef } from "react";
import {
  FieldValues,
  useController,
  UseControllerProps,
  useFormContext,
} from "react-hook-form";
import {
  Modal,
  Platform,
  TouchableOpacity,
  View,
  TextInput,
} from "react-native";
import { AppText, AppView } from "../common";
import ColorPicker, {
  Panel5,
  Preview,
  Swatches,
  ColorPickerRef,
} from "reanimated-color-picker";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { runOnJS } from "react-native-reanimated";

// Define the Group type
export type Group = {
  id?: string;
  name: string;
  color: string;
};

type FormGroupPickerProps<T extends FieldValues> = UseControllerProps<T> & {
  label?: string;
  placeholder?: string;
  containerClass?: string;
  labelClass?: string;
  pickerClass?: string;
  errorClass?: string;
  maxRecentColors?: number;
};

export default function FormGroupPicker<T extends FieldValues>({
  name,
  label,
  placeholder = "Select a group",
  control,
  containerClass,
  labelClass,
  pickerClass,
  errorClass,
  maxRecentColors = 5,
}: FormGroupPickerProps<T>) {
  const [modalVisible, setModalVisible] = useState(false);
  const [recentColors, setRecentColors] = useState<string[]>([]);
  const [currentPickedColor, setCurrentPickedColor] = useState<string | null>(
    null
  );
  const [currentGroupName, setCurrentGroupName] = useState<string>("");
  const colorPickerRef = useRef<ColorPickerRef>(null);

  const form = useFormContext();
  const { field, fieldState } = useController({
    control: control ?? form.control,
    name,
  });

  const primaryColor = useThemeColor("primary");
  const { currentTheme } = useTheme();
  const isDark = currentTheme === "dark";

  // Load recent colors from storage
  useEffect(() => {
    const loadRecentColors = async () => {
      try {
        const savedColors = await AsyncStorage.getItem("recentColors");
        if (savedColors) {
          setRecentColors(JSON.parse(savedColors));
        }
      } catch (error) {
        console.error("Failed to load recent colors:", error);
      }
    };

    loadRecentColors();
  }, []);

  // Save a color to recent colors
  const saveToRecentColors = async (color: string) => {
    try {
      const updatedColors = [color, ...recentColors.filter((c) => c !== color)];
      const limitedColors = updatedColors.slice(0, maxRecentColors);
      setRecentColors(limitedColors);
      await AsyncStorage.setItem("recentColors", JSON.stringify(limitedColors));
    } catch (error) {
      console.error("Failed to save recent colors:", error);
    }
  };

  // Update color value from UI thread
  const updateColorValue = (hexColor: string) => {
    setCurrentPickedColor(hexColor);
  };

  // Apply the selected group and close the modal
  const applySelectedGroup = () => {
    if (currentPickedColor && currentGroupName.trim()) {
      const group: Group = {
        name: currentGroupName.trim(),
        color: currentPickedColor,
      };
      field.onChange(group);
      saveToRecentColors(currentPickedColor);
    }
    setModalVisible(false);
  };

  // Worklet-compatible handler for the color picker
  const handleColorChange = (color: { hex: string }) => {
    "worklet";
    runOnJS(updateColorValue)(color.hex);
  };

  // When opening the modal, set the current values
  const handleOpenModal = () => {
    if (field.value) {
      setCurrentGroupName(field.value.name || "");
      setCurrentPickedColor(field.value.color || "#000000");
    } else {
      setCurrentGroupName("");
      setCurrentPickedColor("#000000");
    }
    setModalVisible(true);
  };

  // Remove the group
  const handleRemoveGroup = () => {
    field.onChange(null);
    setModalVisible(false);
  };

  const renderMobileGroupPicker = () => (
    <>
      <TouchableOpacity
        onPress={handleOpenModal}
        className={cn(
          "flex-row justify-between items-center p-3 border border-gray-300 rounded-lg",
          fieldState.error && "border-red-500",
          pickerClass
        )}
      >
        <AppView className="flex-row items-center">
          {field.value ? (
            <>
              <View
                className="w-6 h-6 rounded-full mr-2"
                style={{ backgroundColor: field.value.color }}
              />
              <AppText>{field.value.name}</AppText>
            </>
          ) : (
            <AppText className="text-gray-500">{placeholder}</AppText>
          )}
        </AppView>
        <Ionicons name="people-outline" size={20} color="gray" />
      </TouchableOpacity>

      <Modal
        visible={modalVisible}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setModalVisible(false)}
      >
        <TouchableOpacity
          activeOpacity={1}
          onPress={() => setModalVisible(false)}
          className="flex-1 bg-black/50"
        >
          <AppView className="flex-1 justify-end">
            <TouchableOpacity activeOpacity={1}>
              <AppView variant="card" className="rounded-t-xl">
                <AppView className="flex-row justify-between items-center p-4 border-b border-gray-200">
                  <AppText variant="h3">{label || "Select Group"}</AppText>
                  <TouchableOpacity
                    onPress={applySelectedGroup}
                    className="flex-row items-center"
                  >
                    <AppView className="flex-row">
                      {field.value && (
                        <TouchableOpacity
                          onPress={handleRemoveGroup}
                          className="flex-row items-center mr-4"
                        >
                          <AppText className="text-red-500 font-medium mr-1">
                            Remove
                          </AppText>
                        </TouchableOpacity>
                      )}
                      <AppText className="text-primary font-medium mr-1">
                        Done
                      </AppText>
                    </AppView>
                  </TouchableOpacity>
                </AppView>

                <AppView className="p-4">
                  <AppText variant="label" className="mb-2">
                    Group Name
                  </AppText>
                  <TextInput
                    value={currentGroupName}
                    onChangeText={setCurrentGroupName}
                    placeholder="Enter group name"
                    className={cn(
                      "p-3 mb-4 border border-gray-300 rounded-lg",
                      isDark ? "text-white bg-gray-800" : "text-black bg-white"
                    )}
                    placeholderTextColor={isDark ? "#9ca3af" : "#6b7280"}
                  />

                  <AppText variant="label" className="mb-2">
                    Group Color
                  </AppText>
                  <ColorPicker
                    ref={colorPickerRef}
                    value={currentPickedColor || "#000000"}
                    onComplete={handleColorChange}
                    onChange={handleColorChange}
                  >
                    <Preview />
                    <Panel5 />
                    {recentColors.length > 0 && (
                      <Swatches
                        colors={recentColors}
                        style={{ marginTop: 16 }}
                      />
                    )}
                  </ColorPicker>
                </AppView>
              </AppView>
            </TouchableOpacity>
          </AppView>
        </TouchableOpacity>
      </Modal>
    </>
  );

  const renderWebGroupPicker = () => (
    <AppView
      className={cn(
        "relative border border-gray-300 rounded-lg overflow-hidden",
        fieldState.error && "border-red-500",
        pickerClass
      )}
    >
      <TouchableOpacity
        onPress={handleOpenModal}
        className="flex-row justify-between items-center p-3"
      >
        <AppView className="flex-row items-center">
          {field.value ? (
            <>
              <View
                className="w-6 h-6 rounded-full mr-2"
                style={{ backgroundColor: field.value.color }}
              />
              <AppText>{field.value.name}</AppText>
            </>
          ) : (
            <AppText className="text-gray-500">{placeholder}</AppText>
          )}
        </AppView>
        <Ionicons name="people-outline" size={20} color="gray" />
      </TouchableOpacity>

      {modalVisible && (
        <Modal
          visible={modalVisible}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setModalVisible(false)}
        >
          <TouchableOpacity
            activeOpacity={1}
            onPress={() => setModalVisible(false)}
            className="flex-1 bg-black/50 items-center justify-center"
          >
            <TouchableOpacity activeOpacity={1}>
              <AppView
                variant="card"
                className="rounded-xl w-80 max-w-[90%]"
                style={{ maxHeight: "80%" }}
              >
                <AppView className="flex-row justify-between items-center p-4 border-b border-gray-200">
                  <AppText variant="h3">{label || "Select Group"}</AppText>
                  <TouchableOpacity
                    onPress={applySelectedGroup}
                    className="flex-row items-center"
                  >
                    <AppText className="text-primary font-medium mr-1">
                      Apply
                    </AppText>
                  </TouchableOpacity>
                </AppView>

                <AppView className="p-4">
                  <AppText variant="label" className="mb-2">
                    Group Name
                  </AppText>
                  <TextInput
                    value={currentGroupName}
                    onChangeText={setCurrentGroupName}
                    placeholder="Enter group name"
                    className={cn(
                      "p-3 mb-4 border border-gray-300 rounded-lg",
                      isDark ? "text-white bg-gray-800" : "text-black bg-white"
                    )}
                    placeholderTextColor={isDark ? "#9ca3af" : "#6b7280"}
                  />

                  <AppText variant="label" className="mb-2">
                    Group Color
                  </AppText>
                  <ColorPicker
                    ref={colorPickerRef}
                    value={currentPickedColor || "#000000"}
                    onComplete={handleColorChange}
                    onChange={handleColorChange}
                  >
                    <Preview />
                    <Panel5 />
                    {recentColors.length > 0 && (
                      <Swatches
                        colors={recentColors}
                        style={{ marginTop: 16 }}
                      />
                    )}
                  </ColorPicker>
                </AppView>

                {field.value && (
                  <AppView className="p-4 border-t border-gray-200">
                    <TouchableOpacity
                      onPress={handleRemoveGroup}
                      className="flex-row items-center justify-center"
                    >
                      <AppText className="text-red-500 font-medium">
                        Remove Group
                      </AppText>
                    </TouchableOpacity>
                  </AppView>
                )}
              </AppView>
            </TouchableOpacity>
          </TouchableOpacity>
        </Modal>
      )}
    </AppView>
  );

  return (
    <AppView className={cn("mb-4", containerClass)}>
      {label && (
        <AppText variant="label" className={cn("mb-1", labelClass)}>
          {label}
        </AppText>
      )}

      {Platform.OS === "web"
        ? renderWebGroupPicker()
        : renderMobileGroupPicker()}

      {fieldState.error && (
        <AppText className={cn("text-red-500 text-sm mt-1", errorClass)}>
          {fieldState.error.message}
        </AppText>
      )}
    </AppView>
  );
}

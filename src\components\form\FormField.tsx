import React, { forwardRef, useEffect } from "react";
import { TextInputProps, TouchableOpacity, TextInput } from "react-native";
import { AppText as Text, AppView as View } from "../common";
import AppTextInput from "../common/TextInput";
import {
  FieldValues,
  useController,
  UseControllerProps,
  useFormContext,
} from "react-hook-form";
import { cn } from "@/lib/utils";
import FormCheckbox from "./FormCheckbox";
import { HideCondition } from "@/lib/types";

type FormFieldProps<T extends FieldValues> = Omit<TextInputProps, "type"> &
  UseControllerProps<T> & {
    label?: string;
    containerClass?: string;
    labelClass?: string;
    inputClass?: string;
    errorClass?: string;
    LeftIcon?: React.ReactNode;
    RightIcon?: React.ReactNode;
    secureTextEntry?: boolean;
    toggleSecure?: () => void;
    type?: "text" | "checkbox";
    hide?: HideCondition; // Use the centralized HideCondition type
  };

const FormField = forwardRef<TextInput, FormFieldProps<any>>(
  (
    {
      name,
      label,
      control,
      containerClass,
      labelClass,
      inputClass,
      errorClass,
      LeftIcon,
      RightIcon,
      secureTextEntry,
      toggleSecure,
      type = "text",
      hide, // Add hide prop
      ...props
    },
    ref
  ) => {
    const form = useFormContext();

    // For checkbox type, we need to handle it differently
    if (type === "checkbox") {
      const { field: checkboxField } = useController({
        control: control ?? form.control,
        name,
      });

      // Check if the field should be hidden based on another field's value
      const shouldHide = hide
        ? form.watch(hide.dependsOn) === hide.hideValue
        : false;

      // Clear the field value when hidden if clearOnHide is true
      useEffect(() => {
        if (shouldHide && hide?.clearOnHide && checkboxField.value) {
          checkboxField.onChange(false);
        }
      }, [shouldHide, hide, checkboxField]);

      if (shouldHide) {
        return null; // Don't render anything if hide condition is met
      }

      return (
        <FormCheckbox
          name={name}
          label={label}
          control={control}
          containerClass={containerClass}
          labelClass={labelClass}
          errorClass={errorClass}
        />
      );
    }

    const { field, fieldState } = useController({
      control: control ?? form.control,
      name,
    });

    // Check if the field should be hidden based on another field's value
    const shouldHide = hide
      ? form.watch(hide.dependsOn) === hide.hideValue
      : false;

    // Clear the field value when hidden if clearOnHide is true
    useEffect(() => {
      if (shouldHide && hide?.clearOnHide && field.value) {
        field.onChange("");
      }
    }, [shouldHide, hide, field]);

    if (shouldHide) {
      return null; // Don't render anything if hide condition is met
    }

    return (
      <View className={cn("mb-4", containerClass)}>
        {label && (
          <Text className={cn("text-sm mb-1", labelClass)}>{label}</Text>
        )}

        <View className="flex-row items-center border border-gray-300 rounded-lg">
          {LeftIcon && <View className="pl-3">{LeftIcon}</View>}

          <AppTextInput
            ref={ref}
            value={field.value as string}
            onChangeText={field.onChange}
            onBlur={field.onBlur}
            className={cn(
              "flex-1 p-3 text-base",
              inputClass,
              fieldState.error && "border-red-500"
            )}
            secureTextEntry={secureTextEntry}
            {...props}
          />

          {RightIcon && (
            <TouchableOpacity onPress={toggleSecure} className="pr-3">
              {RightIcon}
            </TouchableOpacity>
          )}
        </View>

        {fieldState.error && (
          <Text className={cn("text-red-500 text-sm mt-1", errorClass)}>
            {fieldState.error.message}
          </Text>
        )}
      </View>
    );
  }
);

export default FormField;

import { AppScrollView, AppT<PERSON>t, AppView } from "@/components/common";
import { Item, Section, UserInfo } from "@/components/profile";
import { useTheme } from "@/context/ThemeContext";
import { useThemeColor } from "@/hooks/useThemeColor";
import { MaterialIcons } from "@expo/vector-icons";
import React from "react";
import { Switch, TouchableOpacity } from "react-native";

export default function Profile() {
  const { setTheme, theme, currentTheme } = useTheme();
  const primaryColor = useThemeColor("primary");
  //console.log(primaryColor);

  return (
    <AppScrollView>
      <AppView className="p-6 space-y-6">
        {/** Account */}
        <UserInfo />

        {/** Preferences */}

        <Section title="App Preferences">
          <Item
            title="Theme (Light/Dark)"
            description="Switch between light and dark visual modes."
            hideIcon
            disabled={theme == "system"}
            onPress={() => setTheme(theme == "light" ? "dark" : "light")}
          >
            <Switch
              trackColor={{ false: "#d1d5db", true: "#d1d5db" }}
              thumbColor={theme == "dark" ? primaryColor : "#ffffff"}
              value={theme == "dark"}
              onChange={() => setTheme(theme == "dark" ? "light" : "dark")}
            />
          </Item>
          <Item
            title="System Theme"
            description="Use the system's theme setting."
            hideIcon
            onPress={() => setTheme(theme == "system" ? "light" : "system")}
          >
            <Switch
              trackColor={{ false: "#d1d5db", true: "#d1d5db" }}
              thumbColor={theme == "system" ? primaryColor : "#ffffff"}
              value={theme == "system"}
              onChange={() => setTheme(theme == "system" ? "light" : "system")}
            />
          </Item>
          <Item
            title="Language"
            description="Select your preferred display language for the app."
          />
          <Item
            title="Data Sync Options"
            description="Manage how your farm data is synchronized and backed up."
          />
        </Section>

        {/** Notifications */}

        <Section title="Notifications">
          <Item
            title="Push Notifications"
            description="Receive instant alerts for critical farm events."
            hideIcon
          >
            <Switch
              trackColor={{ false: "#d1d5db", true: "#d1d5db" }}
              thumbColor="#ffffff"
              value={true}
            />
          </Item>
          <Item
            title="Email Notifications"
            description="Get important updates and summaries via email."
            hideIcon
          >
            <Switch
              trackColor={{ false: "#d1d5db", true: "#d1d5db" }}
              thumbColor="#ffffff"
              value={false}
            />
          </Item>
          <Item
            title="Event Reminders"
            description="Get reminders for upcoming farm activities and tasks."
            hideIcon
          >
            <Switch
              trackColor={{ false: "#d1d5db", true: "#d1d5db" }}
              thumbColor="#ffffff"
              value={true}
            />
          </Item>
        </Section>

        {/** Support & Legal */}

        <Section title="Support & Legal">
          <Item
            title="Help & Support"
            description="Contact our support team for assistance."
          />
          <Item
            title="Terms of Service"
            description="Review FarmTrack's terms and conditions for use."
          />
          <Item
            title="Privacy Policy"
            description="Understand how your personal and farm data is handled."
          />
          <TouchableOpacity className="p-4 hover:bg-gray-50">
            <AppView className="flex flex-row items-center justify-between">
              <AppView className="flex flex-1">
                <AppText className="text-sm font-medium text-red-600">
                  Sign Out
                </AppText>
                <AppText className="text-xs text-gray-500">
                  Log out of your FarmTrack account securely.
                </AppText>
              </AppView>
              <MaterialIcons name="logout" size={24} color="#dc2626" />
            </AppView>
          </TouchableOpacity>
        </Section>
      </AppView>
    </AppScrollView>
  );
}

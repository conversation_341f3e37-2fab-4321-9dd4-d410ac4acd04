import { AppText, AppView } from "@/components/common";
import { useTheme } from "@/context/ThemeContext";

interface SectionProp {
  title: string;
  children: React.ReactNode | React.ReactNode[];
}

export default function Section({ title, children }: SectionProp) {
  const { currentTheme } = useTheme();
  return (
    <AppView
      className={`rounded-lg border ${
        currentTheme == "dark" ? "border-gray-700" : "border-gray-200"
      } mb-4`}
    >
      <AppText variant="h3" className=" font-semibold  p-4 ">
        {title}
      </AppText>
      <AppView
        className={`divide-y ${
          currentTheme == "dark" ? "divide-gray-700" : "divide-gray-200"
        }
          `}
      >
        {children}
      </AppView>
    </AppView>
  );
}

import React from 'react';
import {
  TouchableOpacity,
  ActivityIndicator,
  TouchableOpacityProps,
} from 'react-native';
import { useFormContext } from 'react-hook-form';
import { Text } from 'react-native';
import { cn } from '@/lib/utils';
import { useSubmitHandler } from './Form';

type SubmitButtonProps = TouchableOpacityProps & {
  isLoading?: boolean;
  label: string;
  className?: string;
  variant?: 'primary' | 'success';
};

export default function SubmitButton({
  isLoading,
  label,
  className,
  variant = 'primary',
  ...props
}: SubmitButtonProps) {
  const { handleSubmit, formState } = useFormContext();
  const onSubmit = useSubmitHandler();

  const variantStyles = {
    primary: 'bg-primary',
    success: 'bg-secondary',
  };

  return (
    <TouchableOpacity
      className={cn(
        'p-4 rounded-lg items-center',
        variantStyles[variant],
        (formState.isSubmitting || isLoading) && 'opacity-50',
        className
      )}
      onPress={handleSubmit(onSubmit)}
      {...props}
      disabled={formState.isSubmitting || isLoading || props.disabled}
    >
      {formState.isSubmitting || isLoading ? (
        <ActivityIndicator color="white" />
      ) : (
        <Text className="text-white font-bold">{label}</Text>
      )}
    </TouchableOpacity>
  );
}

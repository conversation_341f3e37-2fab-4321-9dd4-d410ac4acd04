import Animated, {
  FadeInDown,
  FadeOutUp,
  Layout,
} from 'react-native-reanimated';
import { AppText } from '../common';
import { cn } from '@/lib/utils';

export default function ErrorSection({
  error,
  className,
}: {
  error: any;
  className?: string;
}) {
  if (!error) return null;

  return (
    <>
      {error ? (
        <Animated.View
          entering={FadeInDown.duration(300).springify()}
          exiting={FadeOutUp.duration(200).springify()}
          layout={Layout.springify()}
          className={cn(
            className,
            'border border-pink-800 p-4 rounded-lg mb-4 bg-pink-700/10'
          )}
        >
          <AppText className="text-center text-red-500">{error}</AppText>
        </Animated.View>
      ) : (
        <Animated.View layout={Layout.springify()} className="h-14" />
      )}
    </>
  );
}

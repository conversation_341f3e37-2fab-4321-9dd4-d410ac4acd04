import { useColorScheme } from "react-native";
import tailwindConfig from "../../tailwind.config";

/** Get the default colors from Tailwind config */

type ThemeColors = NonNullable<
  NonNullable<typeof tailwindConfig.theme>["extend"]
>["colors"];

// Get keys of colors that have a DEFAULT property
type ColorKeys = {
  [K in keyof ThemeColors]: ThemeColors[K] extends { DEFAULT: string }
    ? K
    : never;
}[keyof ThemeColors];

export function useThemeColor(colorName: ColorKeys) {
  const theme = useColorScheme();
  const isDark = theme === "dark";

  const color = tailwindConfig.theme?.extend?.colors?.[colorName] ?? undefined;

  if (typeof color === "object" && "DEFAULT" in color) {
    return isDark ? color.dark : color.DEFAULT;
  }

  return color;
}

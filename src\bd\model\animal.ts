import { Model, tableSchema } from "@nozbe/watermelondb";
import { Table } from "../enum/table";
import { AnimalFields } from "../enum/fields/animal";
import { field } from "@nozbe/watermelondb/decorators";

const {
  tagNumber,
  name,
  breed,
  damTag,
  sireTag,
  dob,
  reproductivity,
  gender,
  color,
  groupId,
  type,
} = AnimalFields;

export default class Animal extends Model {
  static table = Table.animals;
  //   static associations = {
  //     [Table.events]: { type: "has_many", foreignKey: "animal_id" },
  //   };
  @field(`${tagNumber}`) tagNumber: string | undefined | null;
  @field(`${name}`) name: string;
  @field(`${breed}`) breed: string;
  @field(`${damTag}`) damTag: string;
  @field(`${sireTag}`) sireTag: string;
  @field(`${dob}`) birthDate: string;
  @field(`${reproductivity}`) reproductiveStatus: string;
  @field(`${gender}`) gender: string;
  @field(`${color}`) color: string;
  @field(`${groupId}`) groupId: string;
  @field(`${type}`) type: string;
}

export const animalSchema = tableSchema({
  name: Table.animals,
  columns: [
    { name: tagNumber, type: "string", isOptional: true },
    { name: name, type: "string", isOptional: true },
    { name: breed, type: "string", isOptional: true },
    { name: damTag, type: "string", isOptional: true },
    { name: sireTag, type: "string", isOptional: true },
    { name: dob, type: "string" },
    { name: reproductivity, type: "string", isOptional: true },
    { name: gender, type: "string" },
    { name: color, type: "string", isOptional: true },
    { name: groupId, type: "string", isOptional: true },
    { name: type, type: "string" },
  ],
});

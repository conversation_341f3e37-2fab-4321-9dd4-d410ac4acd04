import React from 'react';
import {
  Pressable,
  PressableProps,
  StyleProp,
  ViewStyle,
} from 'react-native';
import { useTheme } from '@/context/ThemeContext';
import { cn } from '@/lib/utils';
import AppText from './Text';

type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
type ButtonSize = 'sm' | 'md' | 'lg';

type AppButtonProps = PressableProps & {
  variant?: ButtonVariant;
  size?: ButtonSize;
  className?: string;
  textClassName?: string;
  style?: StyleProp<ViewStyle>;
  onPress?: () => void;
  children: React.ReactNode;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  isLoading?: boolean;
  disabled?: boolean;
};

const AppButton = React.forwardRef<React.ComponentRef<typeof Pressable>, AppButtonProps>(({
  variant = 'primary',
  size = 'md',
  className = '',
  textClassName = '',
  style,
  onPress,
  children,
  leftIcon,
  rightIcon,
  isLoading = false,
  disabled = false,
  ...props
}, ref) => {
  const { currentTheme } = useTheme();
  const isDark = currentTheme === 'dark';

  const sizes = {
    sm: 'py-1.5 px-3',
    md: 'py-2.5 px-4',
    lg: 'py-3 px-5',
  };

  const variants = {
    primary: 'bg-primary',
    secondary: isDark ? 'bg-gray-700' : 'bg-gray-200',
    outline: 'border border-primary bg-transparent',
    ghost: 'bg-transparent',
    danger: 'bg-red-500',
  };

  const textColors = {
    primary: 'text-white',
    secondary: isDark ? 'text-white' : 'text-gray-800',
    outline: 'text-primary',
    ghost: isDark ? 'text-white' : 'text-gray-800',
    danger: 'text-white',
  };

  const disabledStyles = disabled || isLoading 
    ? 'opacity-50' 
    : '';

  return (
    <Pressable
      ref={ref}
      className={cn(
        'rounded-lg items-center justify-center flex-row',
        sizes[size],
        variants[variant],
        disabledStyles,
        className
      )}
      style={style}
      onPress={onPress}
      disabled={disabled || isLoading}
      {...props}
    >
      {leftIcon && <View className="mr-2">{leftIcon}</View>}
      
      {typeof children === 'string' ? (
        <AppText
          variant="button"
          className={cn(textColors[variant], textClassName)}
        >
          {isLoading ? 'Loading...' : children}
        </AppText>
      ) : (
        children
      )}
      
      {rightIcon && <View className="ml-2">{rightIcon}</View>}
    </Pressable>
  );
});

// Add display name for better debugging
AppButton.displayName = 'AppButton';

// Helper View component for icons
function View({ 
  children, 
  className = '' 
}: {
  children: React.ReactNode;
  className?: string;
}) {
  return <Pressable className={className}>{children}</Pressable>;
}

export default AppButton;

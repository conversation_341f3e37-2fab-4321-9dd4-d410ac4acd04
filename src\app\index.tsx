import database, { animalCollection, postsCollection } from "@/bd";
import Events from "@/screens/events/list";
import { Link } from "expo-router";
import React from "react";
import { Text, TouchableOpacity, View } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { onAdd as addAnimal, onView as viewAnimal } from "@/bd/queries/animal";

export default function Page() {
  return (
    <View className="flex flex-1 bg-white">
      <Header />
      <Content />
      <Footer />
    </View>
  );
}

function Content() {
  // const onAdd = async () => {
  //   await database.write(async () => {
  //     await postsCollection.create((post) => {
  //       post.title = "Hello World";
  //       post.subtitle = "Hello World";
  //       post.body = "Hello World";
  //       post.isPinned = false;
  //     });
  //   });
  // };

  const onView = async () => {
    const posts = await postsCollection.query().fetch();
    console.log(posts);
  };

  const onAdd = async () => {
    const animalToAdd = {
      name: "<PERSON>",
      image: "",
      breed: "<PERSON>",
      tagNumber: "1234",
      damTag: "4321",
      sireTag: "1234",
      dob: "2020-01-01",
      reproductivity: "Intact",
      gender: "Female",
      color: "Black",
      groupId: "1",
      type: "Cattle",
    };

    await database.write(async () => {
      await animalCollection.create((animal) => {
        // for (const key in animal) {
        //   animal[key] = animalToAdd[key];
        // }
        animal.name = animalToAdd.name;
        animal.image = animalToAdd.image;
        animal.breed = animalToAdd.breed;
        animal.tagNumber = animalToAdd.tagNumber;
        animal.damTag = animalToAdd.damTag;
        animal.sireTag = animalToAdd.sireTag;
        animal.birthDate = animalToAdd.dob;
        animal.reproductiveStatus = animalToAdd.reproductivity;
        animal.gender = animalToAdd.gender;
        animal.color = animalToAdd.color;
        animal.groupId = animalToAdd.groupId;
        animal.type = animalToAdd.type;
      });
    });
  };

  const view = async () => {
    const animals = await viewAnimal();
    console.log(animals);
  };

  return (
    <View className="flex-1">
      <View className="flex flex-1 justify-center items-center">
        <TouchableOpacity onPress={onAdd}>
          <Text className="border  border-slate-400 p-2 m-2">
            Test Add Animal
          </Text>
        </TouchableOpacity>
        <TouchableOpacity onPress={view}>
          <Text className="border  border-slate-400 p-2">Test View Animal</Text>
        </TouchableOpacity>
        <TouchableOpacity onPress={onView}>
          <Text className="border  border-slate-400 p-2">View ALL Posts</Text>
        </TouchableOpacity>
      </View>
      <View className="flex flex-1 justify-center items-center"></View>
      <Link href="/eventsss">alal</Link>
    </View>
  );
}

function Header() {
  const { top } = useSafeAreaInsets();
  return (
    <View style={{ paddingTop: top }}>
      <View className="px-4 lg:px-6 h-14 flex items-center flex-row justify-between ">
        <Link className="font-bold flex-1 items-center justify-center" href="/">
          ACME
        </Link>
        <View className="flex flex-row gap-4 sm:gap-6">
          <Link
            className="text-md font-medium hover:underline web:underline-offset-4"
            href="/events"
          >
            Events
          </Link>
          <Link
            className="text-md font-medium hover:underline web:underline-offset-4"
            href="/animals"
          >
            Animals
          </Link>
          <Link
            className="text-md font-medium hover:underline web:underline-offset-4"
            href="/profile"
          >
            Profile
          </Link>
        </View>
      </View>
    </View>
  );
}

function Footer() {
  const { bottom } = useSafeAreaInsets();
  return (
    <View
      className="flex shrink-0 bg-gray-100 native:hidden"
      style={{ paddingBottom: bottom }}
    >
      <View className="py-6 flex-1 items-start px-4 md:px-6 ">
        <Text className={"text-center text-gray-700"}>
          © {new Date().getFullYear()} Me
        </Text>
      </View>
    </View>
  );
}

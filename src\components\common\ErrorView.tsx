import { AppText, AppView, AppButton } from '@/components/common';
import { useTheme } from '@/context/ThemeContext';
import { Feather } from '@expo/vector-icons';
import Animated, { FadeInDown } from 'react-native-reanimated';

type ErrorViewProps = {
  error: string;
  onRetry?: () => void;
  className?: string;
};

export function ErrorView({ error, onRetry, className = '' }: ErrorViewProps) {
  const { currentTheme } = useTheme();
  const isDark = currentTheme === 'dark';

  return (
    <Animated.View
      entering={FadeInDown.duration(300).springify()}
      className={`flex-1 items-center justify-center p-4 ${className}`}
    >
      <AppView
        className={`p-6 rounded-2xl ${
          isDark ? 'bg-gray-800' : 'bg-gray-50'
        } w-full max-w-sm items-center`}
      >
        <Feather
          name="alert-circle"
          size={48}
          color={isDark ? '#EF4444' : '#DC2626'}
        />

        <AppText variant="h2" className="mt-4 text-center">
          Oops!
        </AppText>

        <AppText
          className={`mt-2 text-center ${
            isDark ? 'text-gray-400' : 'text-gray-600'
          }`}
        >
          {error}
        </AppText>

        {onRetry && (
          <AppButton variant="outline" className="mt-6" onPress={onRetry}>
            Try Again
          </AppButton>
        )}
      </AppView>
    </Animated.View>
  );
}

import { ScrollView as RNScrollView, ScrollViewProps } from 'react-native';
import { useTheme } from '@/context/ThemeContext';
import { cn } from '@/lib/utils';

const variants = {
  primary: 'flex-1',
  card: 'rounded-lg',
  modal: 'flex-1 px-4',
  list: 'flex-1 px-4 py-2',
  horizontal: 'flex-row',
  default: '',
} as const;

type Variant = keyof typeof variants;

type AppScrollViewProps = ScrollViewProps & {
  variant?: Variant;
  className?: string;
  children: React.ReactNode;
};

export default function AppScrollView({
  children,
  variant = 'default',
  className = '',
  ...props
}: AppScrollViewProps) {
  const { currentTheme } = useTheme();
  const isDark = currentTheme === 'dark';

  const getBackgroundColor = () => {
    switch (variant) {
      case 'card':
        return isDark ? 'bg-gray-800' : 'bg-white';
      case 'modal':
      case 'list':
        return isDark ? 'bg-gray-900' : 'bg-gray-50';
      default:
        return isDark ? 'bg-gray-800' : 'bg-white';
    }
  };

  return (
    <RNScrollView
      className={cn(
        variants[variant],
        getBackgroundColor(),
        className
      )}
      showsVerticalScrollIndicator={false}
      showsHorizontalScrollIndicator={false}
      {...props}
    >
      {children}
    </RNScrollView>
  );
}
import { BasicDetails } from "@/components/animals";
import { AppText, AppView } from "@/components/common";
import {
  Slot,
  useRouter,
  useLocalSearchParams,
  usePathname,
} from "expo-router";
import { View, Text, Pressable } from "react-native";
import Animated, { FadeIn, FadeOut } from "react-native-reanimated";

export default function AnimalLayout() {
  const router = useRouter();
  const { id } = useLocalSearchParams();
  const pathname = usePathname();

  const tabs = ["events", "offspring", "health"];

  const lastPart = pathname.split("/").filter(Boolean).pop();

  const nonActiveTab = !tabs.includes(lastPart);

  const navigate = (tab: string) => {
    router.replace(`/animal/${id}/${tab}`);
  };

  const isActive = (tab: string) => pathname.includes(tab);

  return (
    <AppView variant="page" className="flex-1  px-4 pt-6">
      {/* Header */}
      <BasicDetails />

      {/* Tabs */}
      <View className="flex-row justify-between  rounded-xl mb-3 mt-4">
        {tabs.map((tab) => (
          <Pressable
            key={tab}
            onPress={() => navigate(tab)}
            className={`flex-1 p-2 items-center ${
              isActive(tab) ? "bg-primary rounded-lg" : ""
            }${nonActiveTab && tab === tabs[0] ? "bg-primary rounded-lg" : ""}`}
          >
            <AppText
              className={`font-medium capitalize ${
                isActive(tab) ? "text-slate-200" : ""
              }
                ${nonActiveTab && tab === tabs[0] ? "text-slate-200" : ""}`}
            >
              {tab}
            </AppText>
          </Pressable>
        ))}
      </View>

      {/* Fade Animated Slot */}
      <Animated.View
        entering={FadeIn.duration(200)}
        exiting={FadeOut.duration(150)}
        style={{ flex: 1 }}
      >
        <Slot />
      </Animated.View>
    </AppView>
  );
}

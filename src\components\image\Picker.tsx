import * as ImageManipulator from "expo-image-manipulator";
import * as ImagePicker from "expo-image-picker";
import React, { useCallback, useState } from "react";
import {
  Alert,
  Image,
  Platform,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from "react-native";

import { AntDesign, Feather, MaterialIcons } from "@expo/vector-icons";

import { cn } from "@/lib/utils";

export type ImagePickerProps = {
  maxImages?: number;
  onImagesChange?: (images: string[]) => void;
  className?: string;
  allowsEditing?: boolean;
  aspect?: [number, number];
};

export default function AdvancedImagePicker({
  maxImages = 5,
  onImagesChange,
  className,
  allowsEditing = true,
  aspect = [1, 1],
}: ImagePickerProps) {
  const [images, setImages] = useState<string[]>([]);
  const [uploadProgress, setUploadProgress] = useState<{
    [key: string]: number;
  }>({});

  const requestPermissions = async () => {
    if (Platform.OS !== "web") {
      const { status } =
        await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== "granted") {
        Alert.alert(
          "Sorry, we need camera roll permissions to make this work!"
        );
        return false;
      }
    }
    return true;
  };

  const processImage = async (uri: string) => {
    if (!allowsEditing) return uri;

    const manipResult = await ImageManipulator.manipulateAsync(
      uri,
      [
        {
          resize: { width: 800 }, // Optimize image size
        },
      ],
      {
        compress: 0.8,
        format: ImageManipulator.SaveFormat.JPEG,
        base64: false,
      }
    );

    return manipResult.uri;
  };

  const handleImagePick = useCallback(
    async (type: "camera" | "library") => {
      const hasPermission = await requestPermissions();
      if (!hasPermission) return;

      try {
        const result = await (type === "camera"
          ? ImagePicker.launchCameraAsync({
              mediaTypes: ImagePicker.MediaTypeOptions.Images,
              allowsEditing,
              aspect,
              quality: 0.8,
            })
          : ImagePicker.launchImageLibraryAsync({
              mediaTypes: ImagePicker.MediaTypeOptions.Images,
              allowsEditing,
              aspect,
              quality: 0.8,
              selectionLimit: maxImages - images.length,
            }));

        if (!result.canceled && result.assets) {
          const newImages = await Promise.all(
            result.assets.map(async (asset) => {
              const processedUri = await processImage(asset.uri);

              // Simulate upload progress
              let progress = 0;
              const interval = setInterval(() => {
                progress += 10;
                setUploadProgress((prev) => ({
                  ...prev,
                  [processedUri]: progress,
                }));
                if (progress >= 100) clearInterval(interval);
              }, 200);

              return processedUri;
            })
          );

          const updatedImages = [...images, ...newImages];
          setImages(updatedImages);
          onImagesChange?.(updatedImages);
        }
      } catch (error) {
        Alert.alert("Error selecting images");
      }
    },
    [images, maxImages, onImagesChange]
  );

  const removeImage = (uri: string) => {
    const filtered = images.filter((img) => img !== uri);
    setImages(filtered);
    onImagesChange?.(filtered);
  };

  return (
    <View className={cn("w-full", className)}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        className="py-4"
      >
        {images.map((uri) => (
          <View key={uri} className="relative mr-4">
            <Image
              source={{ uri }}
              className="w-32 h-32 rounded-lg bg-gray-100"
            />

            <View className="absolute top-0 left-0 right-0 h-1 bg-gray-200">
              <View
                className="h-full bg-blue-500"
                style={{ width: `${uploadProgress[uri] || 0}%` }}
              />
            </View>

            <TouchableOpacity
              className="absolute -top-2 -right-2 bg-red-500 rounded-full p-1"
              onPress={() => removeImage(uri)}
            >
              <AntDesign name="close" size={16} color="white" />
            </TouchableOpacity>
          </View>
        ))}

        {images.length < maxImages && (
          <View className="flex-row">
            <TouchableOpacity
              className="w-32 h-32 border-2 border-dashed border-gray-300 rounded-lg items-center justify-center mr-4"
              onPress={() => handleImagePick("library")}
            >
              <MaterialIcons name="photo-library" size={32} color="#6b7280" />
              <Text className="text-gray-500 mt-2 text-center">
                {maxImages - images.length} remaining
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              className="w-32 h-32 border-2 border-dashed border-gray-300 rounded-lg items-center justify-center"
              onPress={() => handleImagePick("camera")}
            >
              <Feather name="camera" size={32} color="#6b7280" />
              <Text className="text-gray-500 mt-2 text-center">Take Photo</Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>
    </View>
  );
}

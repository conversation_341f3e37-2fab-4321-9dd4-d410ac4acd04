import { Pressable, Image } from "react-native";
import { AppText, AppView, Badge } from "../common";
import { Animal } from "@/data";
import { useTheme } from "@/context/ThemeContext";
import React from "react";
import { MaterialIcons } from "@expo/vector-icons";
import { Link } from "expo-router";

export default function AnimalItem({
  item,
  width,
}: {
  item: Animal;
  width: number;
}) {
  const { currentTheme } = useTheme();
  const { image, labelColor, age, detail, type, label, name } = item;

  return (
    <Link
      href={`/animal/${item.name}`}
      className="hover:scale-105 hover:opacity-90"
    >
      <AppView
        variant="container"
        className={`rounded-lg shadow m-2 border ${
          currentTheme === "light" ? "border-gray-300" : "border-gray-700"
        } `}
        style={{ width: width }}
      >
        <AppView className="relative">
          <Image source={{ uri: image }} className="w-full h-32 rounded-t-lg" />
          {label && <Badge labelColor={labelColor}>{label}</Badge>}
        </AppView>
        <AppView className="p-4">
          <AppText
            className={`text-md font-semibold ${
              currentTheme === "dark" ? `text-gray-200` : "text-gray-800"
            }`}
          >
            {name}
          </AppText>
          <AppText className="text-xs text-gray-400">
            {type} • {age}
          </AppText>
          <AppView className="flex-row items-center">
            <MaterialIcons
              name={item.detailIcon}
              size={14}
              className={`${item.detailIconColor} mr-1`}
            />
            <AppText className="text-xs">{detail}</AppText>
          </AppView>
        </AppView>
      </AppView>
    </Link>
  );
}

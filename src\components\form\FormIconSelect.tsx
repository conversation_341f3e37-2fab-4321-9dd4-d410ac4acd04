import { useTheme } from "@/context/ThemeContext";
import { useThemeColor } from "@/hooks/useThemeColor";
import { cn } from "@/lib/utils";
import { HideCondition } from "@/lib/types";
import { MaterialCommunityIcons, MaterialIcons } from "@expo/vector-icons";
import React, { useEffect } from "react";
import {
  FieldValues,
  useController,
  UseControllerProps,
  useFormContext,
} from "react-hook-form";
import { TouchableOpacity } from "react-native";
import { AppText, AppView } from "../common";

export type IconSelectOption = {
  label: string;
  value: string | boolean;
  icon: keyof typeof MaterialCommunityIcons.glyphMap; //string; // MaterialIcons name
};

type FormIconSelectProps<T extends FieldValues> = UseControllerProps<T> & {
  label?: string;
  options: IconSelectOption[];
  defaultValue?: string | boolean;
  containerClass?: string;
  labelClass?: string;
  optionsContainerClass?: string;
  errorClass?: string;
  hide?: HideCondition; // Use the centralized HideCondition type
};

export default function FormIconSelect<T extends FieldValues>({
  name,
  label,
  options,
  defaultValue,
  control,
  containerClass,
  labelClass,
  optionsContainerClass,
  errorClass,
  hide, // Add hide prop
}: FormIconSelectProps<T>) {
  const form = useFormContext();
  const { field, fieldState } = useController({
    control: control ?? form.control,
    name,
  });
  const primaryColor = useThemeColor("primary");
  const { currentTheme } = useTheme();
  const isDark = currentTheme === "dark";

  // Check if the field should be hidden based on another field's value
  // Move this AFTER all hooks are called
  const shouldHide = hide
    ? form.watch(hide.dependsOn) === hide.hideValue
    : false;

  // Clear the field value when hidden if clearOnHide is true
  useEffect(() => {
    if (shouldHide && hide?.clearOnHide && field.value) {
      field.onChange(undefined);
    }
  }, [shouldHide, hide, field]);

  if (shouldHide) {
    return null; // Don't render anything if hide condition is met
  }

  // Set default value if provided and no value is already set
  useEffect(() => {
    if (defaultValue !== undefined && !field.value) {
      field.onChange(defaultValue);
    }
  }, [defaultValue, field]);

  return (
    <AppView className={cn("mb-4", containerClass)}>
      {label && (
        <AppText variant="label" className={cn("mb-1", labelClass)}>
          {label}
        </AppText>
      )}

      <AppView className={cn("flex-row flex-wrap", optionsContainerClass)}>
        {options.map((option) => {
          const isSelected = field.value === option.value;
          return (
            <TouchableOpacity
              key={option.value.toString()}
              onPress={() => field.onChange(option.value)}
              className={cn(
                "m-1 p-3 rounded-lg border flex-row items-center",
                isSelected
                  ? "bg-primary/10 border-primary"
                  : isDark
                  ? "border-gray-700 bg-gray-800"
                  : "border-gray-300 bg-white"
              )}
            >
              <MaterialCommunityIcons
                name={option.icon}
                size={20}
                color={isSelected ? primaryColor : "gray"}
              />
              <AppText
                className={cn("ml-2", isSelected && "text-primary font-medium")}
              >
                {option.label}
              </AppText>
            </TouchableOpacity>
          );
        })}
      </AppView>

      {fieldState.error && (
        <AppText className={cn("text-red-500 text-sm mt-1", errorClass)}>
          {fieldState.error.message}
        </AppText>
      )}
    </AppView>
  );
}

import type { Config } from "tailwindcss";

const config = {
  content: ["./src/**/*.{js,jsx,ts,tsx}"],
  presets: [require("nativewind/preset")],
  theme: {
    extend: {
      colors: {
        primary: {
          DEFAULT: "#FF4B55",
          dark: "#E63E47",
          light: "#FF6B73",
        },
        secondary: {
          DEFAULT: "#1F2937",
          dark: "#111827",
          light: "#374151",
        },
      },
    },
  },
  plugins: [],
} satisfies Config;

export default config;

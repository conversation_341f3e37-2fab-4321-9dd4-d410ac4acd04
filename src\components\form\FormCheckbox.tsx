import React from "react";
import { TouchableOpacity } from "react-native";
import { AppText as Text, AppView as View } from "../common";
import { Checkbox } from "@/components/ui";
import {
  FieldValues,
  useController,
  UseControllerProps,
  useFormContext,
} from "react-hook-form";
import { cn } from "@/lib/utils";

type FormCheckboxProps<T extends FieldValues> = UseControllerProps<T> & {
  label?: string;
  containerClass?: string;
  labelClass?: string;
  errorClass?: string;
};

export default function FormCheckbox<T extends FieldValues>({
  name,
  label,
  control,
  containerClass,
  labelClass,
  errorClass,
}: FormCheckboxProps<T>) {
  const form = useFormContext();
  const { field, fieldState } = useController({
    control: control ?? form.control,
    name,
  });

  return (
    <View className={cn("mb-4", containerClass)}>
      <TouchableOpacity
        activeOpacity={0.7}
        onPress={() => field.onChange(!field.value)}
        className="flex-row items-center"
      >
        <Checkbox
          id={name}
          checked={field.value || false}
          onChange={field.onChange}
          className="mr-2"
        />
        {label && (
          <Text className={cn("text-sm flex-1", labelClass)}>{label}</Text>
        )}
      </TouchableOpacity>
      {fieldState.error && (
        <Text className={cn("text-red-500 text-sm mt-1", errorClass)}>
          {fieldState.error.message}
        </Text>
      )}
    </View>
  );
}

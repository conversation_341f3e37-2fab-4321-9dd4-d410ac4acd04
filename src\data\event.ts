import { MaterialIcons } from "@expo/vector-icons";

interface AnimalEvent {
  icon: keyof typeof MaterialIcons.glyphMap;
  iconColor: string;
  bgColor: string;
  date: string;
  title: string;
  description: string;
}

const recentEvents = [
  {
    icon: "cake",
    iconColor: "text-blue-500",
    bgColor: "bg-blue-100",
    date: "Jan 15, 2023",
    title: "Birth",
    description: "Gave birth to 2 healthy kids.",
  },
  {
    icon: "colorize",
    iconColor: "text-indigo-500",
    bgColor: "bg-indigo-100",
    date: "Feb 28, 2023",
    title: "Vaccination",
    description: "Annual CDT vaccination",
  },
  {
    icon: "medical-services",
    iconColor: "text-teal-500",
    bgColor: "bg-teal-100",
    date: "Mar 10, 2023",
    title: "Treatment",
    description: "Treated for minor hoof infection.",
  },
  {
    icon: "science",
    iconColor: "text-pink-500",
    bgColor: "bg-pink-100",
    date: "Apr 05, 2024",
    title: "Sale",
    description: "Sold for breeding purposes to a new owner.",
  },
  {
    icon: "edit",
    iconColor: "text-gray-500",
    bgColor: "bg-gray-100",
    date: "May 15, 2023",
    title: "Health Check",
    description: "Routine health check-up.",
  },
  {
    icon: "edit",
    iconColor: "text-gray-500",
    bgColor: "bg-gray-100",
    date: "May 15, 2023",
    title: "Health Check",
    description: "Routine health check-up.",
  },
  {
    icon: "edit",
    iconColor: "text-gray-500",
    bgColor: "bg-gray-100",
    date: "May 15, 2023",
    title: "Health Check",
    description: "Routine health check-up.",
  },
] as Event[];

const allevents = [
  {
    date: "Oct 26, 2023",
    events: [
      {
        icon: "edit",
        title: "Vaccination",
        description: "Annual booster for all cattle.",
        tag: "Herd",
        date: "Oct 26, 2023",
        time: "10:00 AM",
      },
      {
        icon: "history",
        title: "Birth",
        description: "Goat gave birth to two healthy kids.",
        tag: "Daisy",
        date: "Oct 26, 2023",
        time: "08:33 AM",
        tagColor: "#4ade80",
        iconColor: "#4ade80",
      },
    ],
  },
  {
    date: "Oct 27, 2023",
    events: [
      {
        icon: "favorite-border",
        title: "Health Check",
        description: "Routine check-up for sheep flock.",
        tag: "Flock",
        date: "Oct 27, 2023",
        time: "02:00 PM",
        tagColor: "#ec4899",
        iconColor: "#ec4899",
      },
    ],
  },
  {
    date: "Oct 28, 2023",
    events: [
      {
        icon: "calendar-today",
        title: "Sale",
        description: "Sale of three young pigs.",
        tag: "Porky",
        date: "Oct 28, 2023",
        time: "09:00 AM",
        tagColor: "#f87171",
        iconColor: "#f87171",
      },
    ],
  },
  {
    date: "Oct 29, 2023",
    events: [
      {
        icon: "calendar-today",
        title: "Equipment Maintenance",
        description: "Tractor service scheduled.",
        date: "Oct 29, 2023",
        time: "01:00 PM",
      },
    ],
  },
];

export { allevents, recentEvents, AnimalEvent };

import React, { useState } from "react";
import {
  View,
  TextInput,
  Image,
  Pressable,
  useWindowDimensions,
  FlatList,
  TouchableOpacity,
} from "react-native";
import { FlashList } from "@shopify/flash-list";
import { AntDesign, MaterialIcons } from "@expo/vector-icons";
import { AppText, AppView, Badge } from "@/components/common";
import { Animal, animals } from "@/data";
import { useTheme } from "@/context/ThemeContext";
import { AnimalItem } from "@/components/animals";
import HideOnKeyboardShow from "@/components/hide-on-keyboard";
import { Link } from "expo-router";
import { ContainCenter } from "@/components/ui";

export default function List() {
  const [activeTab, setActiveTab] = useState("All");
  const { currentTheme } = useTheme();
  const { width } = useWindowDimensions();

  // Dynamically set number of columns
  const itemMinWidth = 160;
  const numColumns = Math.max(1, Math.floor(width / itemMinWidth));
  const itemWidth = width / numColumns - 16;

  const filteredAnimals =
    activeTab === "All"
      ? animals
      : animals.filter((animal) => animal.type === activeTab);

  return (
    <AppView variant="page" className="flex-1">
      {/* Search */}

      <AppView className="p-4">
        <AppView className="relative">
          <MaterialIcons
            name="search"
            size={20}
            className="absolute left-3 top-2.5 text-gray-400"
          />
          <TextInput
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500"
            placeholder="Search by name or ID..."
          />
        </AppView>
      </AppView>

      {/* Animal List */}
      <FlashList
        data={filteredAnimals}
        renderItem={({ item }) => <AnimalItem item={item} width={itemWidth} />}
        keyExtractor={(_, index) => String(index)}
        numColumns={numColumns}
        estimatedItemSize={200}
        contentContainerStyle={{ padding: 8 }}
      />
      <FloatingAddButton />
    </AppView>
  );

  function FloatingAddButton() {
    return (
      <HideOnKeyboardShow>
        <Link href="/animals/add" asChild>
          <TouchableOpacity
            className="absolute bottom-5 right-5 items-center justify-center rounded-full"
            style={{
              width: 70,
              height: 70,
              backgroundColor: "rgba(52, 52, 52, 0.5)",
            }}
          >
            <AntDesign name="plus" size={24} color="rgba(255, 255, 255, 0.7)" />
          </TouchableOpacity>
        </Link>
      </HideOnKeyboardShow>
    );
  }
}

import React, { createContext, useContext, useEffect, useState } from "react";
import {
  useColorScheme,
  Appearance,
  AppState,
  AppStateStatus,
  Platform,
  StyleSheet,
} from "react-native";
import AsyncStorage from "@react-native-async-storage/async-storage";

type ThemeMode = "light" | "dark" | "system";

interface ThemeContextType {
  theme: ThemeMode;
  currentTheme: "light" | "dark";
  setTheme: (theme: ThemeMode) => void;
}

const THEME_STORAGE_KEY = "@app_theme_preference";

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const systemColorScheme = useColorScheme() || "light";
  const [theme, setThemeState] = useState<ThemeMode>("system");
  const [isLoading, setIsLoading] = useState(true);

  // Get the actual theme based on system or user preference
  const currentTheme = theme === "system" ? systemColorScheme : theme;

  // Load saved theme preference
  useEffect(() => {
    const loadTheme = async () => {
      try {
        const savedTheme = await AsyncStorage.getItem(THEME_STORAGE_KEY);
        if (savedTheme) {
          setThemeState(savedTheme as ThemeMode);
        }
      } catch (error) {
        console.error("Failed to load theme preference:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadTheme();
  }, []);

  // Listen for system theme changes
  useEffect(() => {
    const subscription = AppState.addEventListener(
      "change",
      (nextAppState: AppStateStatus) => {
        if (nextAppState === "active" && theme === "system") {
          // Force update when app becomes active to catch system theme changes
          setThemeState("system");
        }
      }
    );

    // For iOS and Android
    const appearanceListener = Appearance.addChangeListener(
      ({ colorScheme }) => {
        if (theme === "system") {
          // Force a re-render when system theme changes
          setThemeState("system");
        }
      }
    );

    return () => {
      subscription.remove();
      appearanceListener.remove();
    };
  }, [theme]);

  // Apply theme to document for web
  useEffect(() => {
    if (Platform.OS === "web" && typeof document !== "undefined") {
      // Set the flag to use class-based dark mode instead of media query
      // if (StyleSheet.setFlag) {
      //   StyleSheet.setFlag('darkMode', 'class');
      // }

      document.documentElement.classList.remove("light", "dark");
      document.documentElement.classList.add(currentTheme);
    }
  }, [currentTheme]);

  const setTheme = async (newTheme: ThemeMode) => {
    setThemeState(newTheme);
    try {
      await AsyncStorage.setItem(THEME_STORAGE_KEY, newTheme);
    } catch (error) {
      console.error("Failed to save theme preference:", error);
    }
  };

  if (isLoading) {
    return null; // Or a loading indicator
  }

  return (
    <ThemeContext.Provider value={{ theme, currentTheme, setTheme }}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error("useTheme must be used within a ThemeProvider");
  }
  return context;
}

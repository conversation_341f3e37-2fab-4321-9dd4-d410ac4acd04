import { MaterialIcons } from "@expo/vector-icons";

interface Animal {
  name: string;
  type: string;
  age: string;
  label: string;
  labelColor: string;
  detail: string;
  detailIcon: keyof typeof MaterialIcons.glyphMap;
  detailIconColor: string;
  image: string;
}

const animals = [
  {
    name: "<PERSON>",
    type: "Cattle",
    age: "3 years",
    label: "Lactating",
    labelColor: "#FFCE1B",
    detail: "85% Milk Yield",
    detailIcon: "eco",
    detailIconColor: "text-green-500",
    image:
      "https://lh3.googleusercontent.com/aida-public/AB6AXuARYkQm_v59iqacklBeDCebSS036Lx0WunuVZhdcJjbxADHd2EKt_-Mrlmf11pbKGgQ8bjiQ3rdTTwPJpQCjBUqFDP8Ut2TkS4YeLC4BCOVeOfugrcMWS0s-2NcnwLI2F5lkIxXWmELD_3K_c2TTxhBqOG3GzoCM3AyROjhHy8MD4puaWPtDR-RtjwOnvT5hirC8rmEFkux6oo3Z01UvbHGX-B2SP5y2p-CsbfY5YH4fjdvtgV9C0no8WC5G1b1j0_Kx-3bG0GLnjqz",
  },
  {
    name: "Billy",
    type: "Flames",
    age: "1.5 years",
    label: "Healthy",
    labelColor: "red",
    detail: "70 kg weight",
    detailIcon: "scale",
    detailIconColor: "text-gray-500",
    image:
      "https://lh3.googleusercontent.com/aida-public/AB6AXuDT3wxjks6eMvichp8QdRgeXpNkXoJMU1mEBcoJZQkaDm-ote8DPJ5S0PpysOWzlzJgtNVzXMLfXAv9zFTQX9XIMtFCDcr9cTNNsyjw7G2wT6R3OYyCa7MLaRrTrIlO585fiJlZ4h4_EfwkFcbYrqiQXhdqablUVXdLHoJOBjqusxqstIMK9aRAcNuSPQcRjNVChc_YXQtvGMy0ZJBSKd_9zXyU-C0jJebovDXA2nDVAFmpNYSvGhCD-T60BGAtukO82BXFa2ZQojFd",
  },
  {
    name: "Rex",
    type: "Rats",
    age: "6 months",
    label: "Healthy",
    labelColor: "#ffffff",
    detail: "1.5 kg weight",
    detailIcon: "scale",
    detailIconColor: "text-gray-500",
    image:
      "https://lh3.googleusercontent.com/aida-public/AB6AXuBKeHKtddWN5W-o96gKLy4QhPOCp18hGMRbSx74pHEO_rD67fnfOhALwAW9JG7UZyDl9wes1kV9v5-am82-e1EnI-aUoC5YxvotWjofokI2-xCgO0fnAZS5EcjK0XWqzOBYUoRmCZwHiAtq8Ab7UfmaaNBvNqCQSby8c4Z3TEW7OrkwTBAqa2xXZ9NgB6D_jCny0g4i4hkD-kgGHvnn23PEQU__8yzBtyFY5JmoS9vMiZzGJ2K7rgzOd6oRVaTtT3qoQG1iV_YFXzP6",
  },
  {
    name: "Penelope",
    type: "Rats",
    age: "8 months",
    metric: "50 kg weight",
    icon: "scale",
    iconColor: "gray",
    image:
      "https://lh3.googleusercontent.com/aida-public/AB6AXuBKeHKtddWN5W-o96gKLy4QhPOCp18hGMRbSx74pHEO_rD67fnfOhALwAW9JG7UZyDl9wes1kV9v5-am82-e1EnI-aUoC5YxvotWjofokI2-xCgO0fnAZS5EcjK0XWqzOBYUoRmCZwHiAtq8Ab7UfmaaNBvNqCQSby8c4Z3TEW7OrkwTBAqa2xXZ9NgB6D_jCny0g4i4hkD-kgGHvnn23PEQU__8yzBtyFY5JmoS9vMiZzGJ2K7rgzOd6oRVaTtT3qoQG1iV_YFXzP6",
    tag: "Growing",
    tagBg: "yellow-100",
    tagText: "yellow-700",
  },
  {
    name: "Henrietta",
    type: "Poultry",
    age: "1 year",
    metric: "5 eggs/week",
    icon: "egg",
    iconColor: "gold",
    image:
      "https://lh3.googleusercontent.com/aida-public/AB6AXuCVT98RBlslUJTyC7aQv41PUml5aERT8L5hLqx0rPaKXoWL4WjsxPAVno7lUo3UC_zgdVdObCEAK3hw2ZLCkIFBKG9VWlIcLJFPOwJXMaLngCOwINQC_VhANU1q9ZMEEkuMBEl7TimMeQQzxtRFKsqcDUQlIegoSBYNeEJv79ofBslg2V3hlDqaGD1e5mNMI-SKrYScP2VMSJ3XuBT2oeOyZrTqjEtY9JEaulYPv168KAD2SH7yOlKPt_aXBmQ3AlBvh_yqbJcUvcnx",
    tag: "Laying Eggs",
    tagBg: "blue-100",
    tagText: "blue-700",
  },
] as Animal[];

export { animals, Animal };

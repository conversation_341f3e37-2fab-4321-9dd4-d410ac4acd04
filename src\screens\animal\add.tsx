import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, A<PERSON><PERSON>iew } from "@/components/common";
import {
  Form,
  FormColorPicker,
  FormDatePicker,
  FormField,
  FormGroupPicker,
  FormIconSelect,
  FormImagePicker,
  SubmitButton,
} from "@/components/form";
import FormSelect from "@/components/form/FormSelect";
import { ContainCenter } from "@/components/ui";
import { useTheme } from "@/context/ThemeContext";
import { router } from "expo-router";
import { Pressable } from "react-native";
import { z } from "zod";
const schema = z.object({
  /** Basic Information */
  eartag: z.string().min(3, `Eartag must be at least ${3} characters`),
  notag: z.boolean(),
  name: z.string().min(3, `Name must be at least ${3} characters`),
  dob: z.date(),
  gender: z.string(),
  color: z.string(),
  images: z.array(z.string()).optional(),
  /** Grouping / Management */
  group: z.object({ name: z.string(), color: z.string() }).optional(),
  /** Genetics / Lineage */
  breed: z.string(),
  dam_tag: z.string(),
  sire_tag: z.string(),
  /** Health / Reproductive */
  reproductive_status: z.string(),
  type: z.string(),
});

type AddAnimalValues = z.infer<typeof schema>;

const sectionSpaceing = "mt-8 mb-4";
const sectionContain = "border rounded-lg border-gray-300  p-4";

export default function add() {
  const { currentTheme } = useTheme();

  return (
    <AppView variant="section" className="flex-1">
      <ContainCenter className="p-4">
        <Form<AddAnimalValues, typeof schema>
          schema={schema}
          initialValues={{
            notag: true,
            eartag: "",
            name: "",
            dob: new Date(),
            gender: "",
            color: "",
            images: [],
            /** Grouping / Management */
            group: {},
            /** Genetics / Lineage */
            breed: "",
            dam_tag: "",
            sire_tag: "",
            /** Health / Reproductive */
            reproductive_status: "",
            type: "",
          }}
          onSubmit={(values) => console.log(values)}
        >
          <AppText variant="h2" className={`${sectionSpaceing}`}>
            Basic Information
          </AppText>
          <AppView className={`${sectionContain}`}>
            <FormImagePicker name="images" />
            {/* <FormField
              name="eartag"
              label="Eartag"
              hide={{ dependsOn: "notag", hideValue: false, clearOnHide: true }}
            /> */}
            <FormIconSelect
              name="notag"
              label="Has Eartag"
              defaultValue={false}
              options={[
                { label: "Yes", value: true, icon: "tag-multiple-outline" },
                { label: "No", value: false, icon: "tag-off-outline" },
              ]}
            />
            <FormField name="name" label="Name" />

            {/* <FormSelect
              name="type"
              label="Type"
              options={[
                { label: "Cattle", value: "cattle" },
                { label: "Poultry", value: "poultry" },
                { label: "Rats", value: "rats" },
              ]}
              hide={{ dependsOn: "notag", hideValue: false, clearOnHide: true }}
            /> */}

            <FormIconSelect
              name="gender"
              label="Gender"
              defaultValue={"female"}
              options={[
                { label: "Male", value: "male", icon: "gender-male" },
                { label: "Female", value: "female", icon: "gender-female" },
              ]}
              hide={{ dependsOn: "notag", hideValue: false }}
            />

            <FormDatePicker name="dob" label="Date of Birth" mode="datetime" />

            <FormColorPicker name="color" label="Color" />
          </AppView>

          <AppText variant="h2" className={`${sectionSpaceing}`}>
            Grouping / Management
          </AppText>
          <AppView className={`${sectionContain}`}>
            <FormGroupPicker
              defaultValue={{ name: "Manga", color: "#000000" }}
              maxRecentColors={10}
              name="group"
              label="Group"
            />
          </AppView>

          <AppText variant="h2" className={`${sectionSpaceing}`}>
            Genetics / Lineage
          </AppText>
          <AppView className={`${sectionContain}`}>
            <FormField name="breed" label="Breed" />
            <FormField name="dam_tag" label="Dam Tag" />
            <FormField name="sire_tag" label="Sire Tag" />
          </AppView>

          <AppText variant="h2" className={`${sectionSpaceing}`}>
            Health / Reproductive
          </AppText>
          <AppView className={`${sectionContain}`}>
            {/* <FormField name="reproductive_status" label="Reproductive Status" /> */}
            <FormIconSelect
              options={[
                {
                  label: "Castrated",
                  value: "castrated",
                  icon: "block-helper",
                },
                { label: "Intact", value: "", icon: "circle" },
              ]}
              name="reproductive_status"
              label="Reproductive Status"
            />
          </AppView>
          <AppView className="flex-row justify-between mt-8 space-x-2">
            <AppButton
              onPress={() => router.replace("/animals")}
              variant="outline"
            >
              Cancel
            </AppButton>
            <SubmitButton label="Add Animal" />
          </AppView>
        </Form>
      </ContainCenter>
    </AppView>
  );
}

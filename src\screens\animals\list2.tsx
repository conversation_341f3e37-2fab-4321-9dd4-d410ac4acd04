import React, { useState, useEffect, useCallback, useMemo } from "react";
import { View, TextInput, useWindowDimensions, ScrollView } from "react-native";
import { FlashList } from "@shopify/flash-list";
import { Q } from "@nozbe/watermelondb";
import { withObservables } from "@nozbe/watermelondb/react";
import { MaterialIcons } from "@expo/vector-icons";
import { AppText, AppView } from "@/components/common";
import { animalCollection } from "@/bd/queries/animal";
import { AnimalItem } from "@/components/animals";
import { Table } from "@/bd/enum/table";
import Animal from "@/bd/model/animal";
import HideOnKeyboardShow from "@/components/hide-on-keyboard";
import { Link, useRouter, useLocalSearchParams } from "expo-router";
import { TouchableOpacity } from "react-native";
import { AntDesign } from "@expo/vector-icons";
import { FloatingButton } from "@/components/ui";
import { Platform } from "react-native";

// Component that renders the list of animals
const AnimalsList = ({ animals }) => {
  const router = useRouter();
  const { width } = useWindowDimensions();
  const params = useLocalSearchParams();

  // Get filters from URL or use defaults
  const [searchQuery, setSearchQuery] = useState(
    params.search?.toString() || ""
  );
  const [activeTab, setActiveTab] = useState(params.type?.toString() || "All");

  // Dynamically set number of columns - memoize to prevent recalculation
  const { numColumns, itemWidth } = useMemo(() => {
    const itemMinWidth = 160;
    const cols = Math.max(1, Math.floor(width / itemMinWidth));
    return {
      numColumns: cols,
      itemWidth: width / cols - 16,
    };
  }, [width]);

  // Update URL when filters change - debounce to prevent too many updates
  useEffect(() => {
    // Skip this effect on non-web platforms to prevent flickering
    if (Platform.OS !== "web") return;

    const timeoutId = setTimeout(() => {
      const queryParams = new URLSearchParams();
      if (searchQuery) {
        queryParams.set("search", searchQuery);
      }
      if (activeTab !== "All") {
        queryParams.set("type", activeTab);
      }
      const queryString = queryParams.toString();
      const path = queryString ? `/animals?${queryString}` : "/animals";

      // Only update URL on web platform
      window.history.replaceState(null, "", path);
    }, 300); // 300ms debounce (was 3000ms)

    return () => clearTimeout(timeoutId);
  }, [searchQuery, activeTab]);

  // Filter animals based on search query and active tab - memoize to prevent recalculation
  const filteredAnimals = useMemo(() => {
    return animals
      .filter(
        (animal) =>
          searchQuery === "" ||
          animal.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          animal.tagNumber?.toLowerCase().includes(searchQuery.toLowerCase())
      )
      .filter((animal) => activeTab === "All" || animal.type === activeTab);
  }, [animals, searchQuery, activeTab]);

  // Get unique animal types for tabs - memoize to prevent recalculation
  const animalTypes = useMemo(() => {
    return ["All", ...new Set(animals.map((animal) => animal.type))];
  }, [animals]);

  // Handle search input change
  const handleSearchChange = useCallback((text) => {
    setSearchQuery(text);
  }, []);

  // Handle tab change
  const handleTabChange = useCallback((type) => {
    setActiveTab(type);
  }, []);

  // Memoize the render item function to prevent recreating on each render
  const renderItem = useCallback(
    ({ item }) => {
      return <AnimalItem item={item} width={itemWidth} />;
    },
    [itemWidth]
  );

  // Memoize the empty component to prevent recreating on each render
  const ListEmptyComponent = useMemo(
    () => (
      <AppView className="flex-1 justify-center items-center p-8">
        <AppText className="text-gray-500 text-center">
          No animals found. Add some animals to get started.
        </AppText>
      </AppView>
    ),
    []
  );

  return (
    <AppView variant="page" className="flex-1">
      {/* Search */}
      <AppView className="p-4">
        <AppView className="relative">
          <MaterialIcons
            name="search"
            size={20}
            className="absolute left-3 top-2.5 text-gray-400"
          />
          <TextInput
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500"
            placeholder="Search by name or ID..."
            value={searchQuery}
            onChangeText={handleSearchChange}
          />
        </AppView>
      </AppView>

      {/* Type Tabs */}
      <AppView className="px-4 mb-2">
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View className="flex-row space-x-2">
            {animalTypes.map((type) => (
              <TouchableOpacity
                key={type}
                onPress={() => handleTabChange(type)}
                className={`px-4 py-2 rounded-full ${
                  activeTab === type ? "bg-primary" : "bg-gray-200"
                }`}
              >
                <AppText
                  className={
                    activeTab === type ? "text-white" : "text-gray-800"
                  }
                >
                  {type}
                </AppText>
              </TouchableOpacity>
            ))}
          </View>
        </ScrollView>
      </AppView>

      {/* Animal List */}
      <FlashList
        data={filteredAnimals}
        renderItem={renderItem}
        keyExtractor={(item) => item.id}
        numColumns={numColumns}
        estimatedItemSize={200}
        contentContainerStyle={{ padding: 8 }}
        ListEmptyComponent={ListEmptyComponent}
        extraData={[searchQuery, activeTab, width]}
      />
      <FloatingButton href="/animals/add" />
    </AppView>
  );
};

// Enhance the component with reactive data from WatermelonDB
const enhance = withObservables([], () => ({
  animals: animalCollection.query().observe(),
}));

// Export the enhanced component
const EnhancedAnimalsList = enhance(AnimalsList);
export default EnhancedAnimalsList;

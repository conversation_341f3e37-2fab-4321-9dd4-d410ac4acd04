import { useTheme } from "@/context/ThemeContext";
import { useThemeColor } from "@/hooks/useThemeColor";
import { cn } from "@/lib/utils";
import { Ionicons } from "@expo/vector-icons";
import React, { useState, useEffect, useRef } from "react";
import {
  FieldValues,
  useController,
  UseControllerProps,
  useFormContext,
} from "react-hook-form";
import { Modal, Platform, TouchableOpacity, View } from "react-native";
import { AppText, AppView } from "../common";
import ColorPicker, {
  Panel5,
  Preview,
  Swatches,
  ColorPickerRef,
} from "reanimated-color-picker";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { runOnJS } from "react-native-reanimated";

type FormColorPickerProps<T extends FieldValues> = UseControllerProps<T> & {
  label?: string;
  placeholder?: string;
  containerClass?: string;
  labelClass?: string;
  pickerClass?: string;
  errorClass?: string;
  maxRecentColors?: number;
};

export default function FormColorPicker<T extends FieldValues>({
  name,
  label,
  placeholder = "Select a color",
  control,
  containerClass,
  labelClass,
  pickerClass,
  errorClass,
  maxRecentColors = 5,
}: FormColorPickerProps<T>) {
  const [modalVisible, setModalVisible] = useState(false);
  const [recentColors, setRecentColors] = useState<string[]>([]);
  const [currentPickedColor, setCurrentPickedColor] = useState<string | null>(
    null
  );
  const colorPickerRef = useRef<ColorPickerRef>(null);

  const form = useFormContext();
  const { field, fieldState } = useController({
    control: control ?? form.control,
    name,
  });

  const primaryColor = useThemeColor("primary");
  const { currentTheme } = useTheme();
  const isDark = currentTheme === "dark";

  // Load recent colors from storage
  useEffect(() => {
    const loadRecentColors = async () => {
      try {
        const savedColors = await AsyncStorage.getItem("recentColors");
        if (savedColors) {
          setRecentColors(JSON.parse(savedColors));
        }
      } catch (error) {
        console.error("Failed to load recent colors:", error);
      }
    };

    loadRecentColors();
  }, []);

  // Save a color to recent colors
  const saveToRecentColors = async (color: string) => {
    try {
      // Add to the beginning and remove duplicates
      const updatedColors = [color, ...recentColors.filter((c) => c !== color)];

      // Limit to maxRecentColors
      const limitedColors = updatedColors.slice(0, maxRecentColors);

      setRecentColors(limitedColors);
      await AsyncStorage.setItem("recentColors", JSON.stringify(limitedColors));
    } catch (error) {
      console.error("Failed to save recent colors:", error);
    }
  };

  // This function will be called from the UI thread via runOnJS
  const updateColorValue = (hexColor: string) => {
    setCurrentPickedColor(hexColor);
  };

  // Apply the selected color and close the modal
  const applySelectedColor = () => {
    if (currentPickedColor) {
      field.onChange(currentPickedColor);
      saveToRecentColors(currentPickedColor);
    }
    setModalVisible(false);
  };

  // Worklet-compatible handler for the color picker
  const handleColorChange = (color: { hex: string }) => {
    "worklet";
    // Use runOnJS to call JS thread functions from the UI thread
    runOnJS(updateColorValue)(color.hex);
  };

  const handleSelectRecentColor = (color: string) => {
    field.onChange(color);
    saveToRecentColors(color);
    setModalVisible(false);
  };

  // When opening the modal, set the current picked color to the field value
  const handleOpenModal = () => {
    setCurrentPickedColor(field.value || "#000000");
    setModalVisible(true);
  };

  const renderMobileColorPicker = () => (
    <>
      <TouchableOpacity
        onPress={handleOpenModal}
        className={cn(
          "flex-row justify-between items-center p-3 border border-gray-300 rounded-lg",
          fieldState.error && "border-red-500",
          pickerClass
        )}
      >
        <AppView className="flex-row items-center">
          {field.value ? (
            <View
              className="w-6 h-6 rounded-full mr-2"
              style={{ backgroundColor: field.value }}
            />
          ) : null}
          <AppText className={cn(!field.value && "text-gray-500")}>
            {field.value || placeholder}
          </AppText>
        </AppView>
        <Ionicons name="color-palette-outline" size={20} color="gray" />
      </TouchableOpacity>

      <Modal
        visible={modalVisible}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setModalVisible(false)}
      >
        <TouchableOpacity
          activeOpacity={1}
          onPress={() => setModalVisible(false)}
          className="flex-1 bg-black/50"
        >
          <AppView className="flex-1 justify-end">
            <TouchableOpacity activeOpacity={1}>
              <AppView variant="card" className="rounded-t-xl">
                <AppView className="flex-row justify-between items-center p-4 border-b border-gray-200">
                  <AppText variant="h3">{label || "Select Color"}</AppText>
                  <TouchableOpacity
                    onPress={applySelectedColor}
                    className="flex-row items-center"
                  >
                    <AppView className="flex-row">
                      {field.value && (
                        <TouchableOpacity
                          onPress={() => {
                            field.onChange("");
                            setCurrentPickedColor(null);
                            setModalVisible(false);
                          }}
                          className="flex-row items-center mr-4"
                        >
                          <AppText className="text-red-500 font-medium mr-1">
                            Remove
                          </AppText>
                        </TouchableOpacity>
                      )}
                      <AppText className="text-primary font-medium mr-1">
                        Done
                      </AppText>
                    </AppView>
                  </TouchableOpacity>
                </AppView>

                <AppView className="p-4">
                  <ColorPicker
                    ref={colorPickerRef}
                    value={field.value || "#000000"}
                    onComplete={handleColorChange}
                    onChange={handleColorChange}
                  >
                    <Preview />
                    <Panel5 />
                    {recentColors.length > 0 && (
                      <Swatches
                        colors={recentColors}
                        style={{ marginTop: 16 }}
                      />
                    )}
                  </ColorPicker>
                </AppView>
              </AppView>
            </TouchableOpacity>
          </AppView>
        </TouchableOpacity>
      </Modal>
    </>
  );

  const renderWebColorPicker = () => (
    <AppView
      className={cn(
        "relative border border-gray-300 rounded-lg overflow-hidden",
        fieldState.error && "border-red-500",
        pickerClass
      )}
    >
      <TouchableOpacity
        onPress={handleOpenModal}
        className="flex-row justify-between items-center p-3"
      >
        <AppView className="flex-row items-center">
          {field.value ? (
            <View
              className="w-6 h-6 rounded-full mr-2"
              style={{ backgroundColor: field.value }}
            />
          ) : null}
          <AppText className={cn(!field.value && "text-gray-500")}>
            {field.value || placeholder}
          </AppText>
        </AppView>
        <Ionicons name="color-palette-outline" size={20} color="gray" />
      </TouchableOpacity>

      {modalVisible && (
        <Modal
          visible={modalVisible}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setModalVisible(false)}
        >
          <TouchableOpacity
            activeOpacity={1}
            onPress={() => setModalVisible(false)}
            className="flex-1 bg-black/50 items-center justify-center"
          >
            <TouchableOpacity activeOpacity={1}>
              <AppView
                variant="card"
                className="rounded-xl w-80 max-w-[90%]"
                style={{ maxHeight: "80%" }}
              >
                <AppView className="flex-row justify-between items-center p-4 border-b border-gray-200">
                  <AppText variant="h3">{label || "Select Color"}</AppText>
                  <TouchableOpacity
                    onPress={applySelectedColor}
                    className="flex-row items-center"
                  >
                    <AppText className="text-primary font-medium mr-1">
                      Apply
                    </AppText>
                  </TouchableOpacity>
                </AppView>

                <AppView className="p-4">
                  <ColorPicker
                    ref={colorPickerRef}
                    value={field.value || "#000000"}
                    onComplete={handleColorChange}
                    onChange={handleColorChange}
                  >
                    <Preview />
                    <Panel5 />
                    {recentColors.length > 0 && (
                      <Swatches
                        colors={recentColors}
                        style={{ marginTop: 16 }}
                      />
                    )}
                  </ColorPicker>
                </AppView>

                {recentColors.length > 0 && (
                  <AppView className="p-4 border-t border-gray-200">
                    <AppText className="text-sm mb-2">Recent Colors</AppText>
                    <AppView className="flex-row flex-wrap">
                      {recentColors.map((color, index) => (
                        <TouchableOpacity
                          key={index}
                          onPress={() => handleSelectRecentColor(color)}
                          className="m-1"
                        >
                          <View
                            className="w-8 h-8 rounded-full border border-gray-300"
                            style={{ backgroundColor: color }}
                          />
                        </TouchableOpacity>
                      ))}
                    </AppView>
                  </AppView>
                )}
              </AppView>
            </TouchableOpacity>
          </TouchableOpacity>
        </Modal>
      )}
    </AppView>
  );

  return (
    <AppView className={cn("mb-4", containerClass)}>
      {label && (
        <AppText variant="label" className={cn("mb-1", labelClass)}>
          {label}
        </AppText>
      )}

      {Platform.OS === "web"
        ? renderWebColorPicker()
        : renderMobileColorPicker()}

      {fieldState.error && (
        <AppText className={cn("text-red-500 text-sm mt-1", errorClass)}>
          {fieldState.error.message}
        </AppText>
      )}
    </AppView>
  );
}

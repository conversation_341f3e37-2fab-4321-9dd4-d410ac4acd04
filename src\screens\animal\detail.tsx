import { BasicDetails } from "@/components/animals";
import { AppText, AppView } from "@/components/common";
import { AnimalEvent, recentEvents } from "@/data/event";
import { useThemeColor } from "@/hooks/useThemeColor";
import { MaterialIcons } from "@expo/vector-icons";
import React from "react";
import { Pressable, ScrollView, TouchableOpacity } from "react-native";

export default function Detail() {
  const primaryColor = useThemeColor("primary");
  return (
    <AppView variant="page" className="flex-1">
      {/* Profile */}
      <ScrollView className="p-4">
        <BasicDetails />
      </ScrollView>
    </AppView>
  );
}

import { Database } from "@nozbe/watermelondb";
import LokiJSAdapter from "@nozbe/watermelondb/adapters/lokijs";
import schema from "./schema";
import migrations from "./migrations";
import Post from "./model/post";
import { Animal } from "./model";

const adapter = new LokiJSAdapter({
  schema,
  migrations,
  useIncrementalIndexedDB: true,
  useWebWorker: false,
  onSetUpError: (err) => console.error("DB setup error (web):", err),
  onQuotaExceededError: (err) => console.warn("IndexedDB quota exceeded:", err),
  extraIncrementalIDBOptions: {
    onDidOverwrite: () => console.warn("IndexedDB overwrite: another tab"),
    onversionchange: () => window.location.reload(),
  },
});

const database = new Database({ adapter, modelClasses: [Post, Animal] });

export default database;

export const postsCollection = database.get<Post>("posts");
export const animalCollection = database.get<Animal>("animals");

console.log("Web DB");

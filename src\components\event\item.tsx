import { AnimalEvent } from "@/data";
import { AppView, AppText } from "../common";
import { Pressable, TouchableOpacity } from "react-native";
import { MaterialIcons } from "@expo/vector-icons";
import { useThemeColor } from "@/hooks/useThemeColor";

export default function EventItem({ event }: { event: AnimalEvent }) {
  const primaryColor = useThemeColor("primary");
  return (
    <AppView variant="borderdCard" className=" p-4 rounded-lg mb-3">
      <Pressable
        className="flex-row items-start"
        onPress={() => console.log("test")}
      >
        <AppView
          className={`${event.bgColor} p-2 rounded-full mr-3 self-center`}
        >
          <MaterialIcons
            name={event.icon}
            size={20}
            className={event.iconColor}
          />
        </AppView>
        <AppView className="flex-1">
          <AppText className="text-xs text-gray-500">{event.date}</AppText>
          <AppText className="font-semibold text-gray-700">
            {event.title}
          </AppText>
          <AppText className="text-sm text-gray-600">
            {event.description}
          </AppText>
        </AppView>
        <TouchableOpacity className="self-center">
          <MaterialIcons name="chevron-right" size={20} color={primaryColor} />
        </TouchableOpacity>
      </Pressable>
    </AppView>
  );
}

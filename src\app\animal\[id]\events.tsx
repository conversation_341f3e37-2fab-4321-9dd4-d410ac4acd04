import { AppText, AppView } from "@/components/common";
import { EventItem } from "@/components/event";
import { FloatingButton } from "@/components/ui";
import { recentEvents } from "@/data/event";
import { useThemeColor } from "@/hooks/useThemeColor";
import { MaterialIcons } from "@expo/vector-icons";
import { useGlobalSearchParams } from "expo-router";
import React from "react";
import { ScrollView, TouchableOpacity } from "react-native";

export default function events() {
  const primaryColor = useThemeColor("primary");
  const { id } = useGlobalSearchParams();

  return (
    <AppView className="flex-1">
      <AppView className="flex-row justify-between items-center mb-4">
        <AppText className="text-xl font-semibold">Recent Events</AppText>
        <TouchableOpacity className="flex-row items-center">
          <MaterialIcons name="add" size={20} color={primaryColor} />
          <AppText className="text-primary ml-1">Add New Event</AppText>
        </TouchableOpacity>
      </AppView>

      <AppView className="flex-1 space-y-3">
        <ScrollView>
          {recentEvents.map((event, idx) => (
            <EventItem key={idx} event={event} />
          ))}
        </ScrollView>
        <FloatingButton
          className="right-0"
          //onPress={() => console.log("test")}
          href={`/animal/${id}/event/add`}
        />
      </AppView>
    </AppView>
  );
}

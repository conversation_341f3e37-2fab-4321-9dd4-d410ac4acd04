import { AppText } from "@/components/common";
import AppView from "@/components/common/View";
import React from "react";
import { MaterialIcons } from "@expo/vector-icons";
import { TouchableOpacity } from "react-native";
import { useTheme } from "@/context/ThemeContext";

type MaterialIconName = keyof typeof MaterialIcons.glyphMap;

interface IconOption {
  name: MaterialIconName;
  color: string;
}

interface ItemProp {
  title: string;
  description: string;
  icon?: IconOption;
  hideIcon?: boolean;
  disabled?: boolean;
  children?: React.ReactNode | React.ReactNode[];
  onPress?: () => void;
}

export default function Item({
  title,
  description,
  icon,
  hideIcon = false,
  disabled = false,
  children,
  onPress,
}: ItemProp) {
  const { currentTheme } = useTheme();
  return (
    <TouchableOpacity
      className={`p-4 hover:${currentTheme == "dark" ? "" : "bg-gray-50"}  ${
        disabled ? "opacity-50" : ""
      }
         
        `}
      disabled={disabled}
      onPress={onPress}
    >
      <AppView className="flex flex-row items-center justify-between">
        <AppView className="flex flex-1 flex-col">
          <AppText variant="body" className="font-medium">
            {title}
          </AppText>
          <AppText variant="caption" className="text-xs ">
            {description}
          </AppText>
        </AppView>

        {!hideIcon && (
          <MaterialIcons
            name={icon ? icon.name : `chevron-right`}
            size={24}
            color={icon ? icon.color : "#9ca3af"}
          />
        )}

        {children}
      </AppView>
    </TouchableOpacity>
  );
}

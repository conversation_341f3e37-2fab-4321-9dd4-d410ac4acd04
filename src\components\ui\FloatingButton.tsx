import { Link, LinkProps } from "expo-router";
import HideOnKeyboardShow from "../hide-on-keyboard";
import { TouchableOpacity } from "react-native";
import { AntDesign } from "@expo/vector-icons";
import { cn } from "@/lib/utils";

type IconName = keyof typeof AntDesign.glyphMap;

type FloatingButtonProps =
  | {
      href: LinkProps["href"];
      onPress?: never;
      icon?: IconName;
      className?: string;
    }
  | {
      href?: never;
      onPress: () => void;
      icon?: IconName;
      className?: string;
    };

export default function FloatingButton({
  href,
  icon,
  className,
  onPress,
}: FloatingButtonProps) {
  const content = (
    <TouchableOpacity
      className={cn(
        "absolute bottom-5 right-5 items-center justify-center rounded-full",
        className
      )}
      style={{
        width: 70,
        height: 70,
        backgroundColor: "rgba(52, 52, 52, 0.5)",
      }}
      onPress={onPress}
    >
      <AntDesign
        name={icon ?? "plus"}
        size={24}
        color="rgba(255, 255, 255, 0.7)"
      />
    </TouchableOpacity>
  );

  return (
    <HideOnKeyboardShow>
      {href ? (
        <Link href={href} asChild>
          {content}
        </Link>
      ) : (
        content
      )}
    </HideOnKeyboardShow>
  );
}

import { useTheme } from "@/context/ThemeContext";
import { useThemeColor } from "@/hooks/useThemeColor";
import { cn } from "@/lib/utils";
import DateTimePicker from "@react-native-community/datetimepicker";
import { Ionicons } from "@expo/vector-icons";
import React, { useEffect, useState } from "react";
import {
  FieldValues,
  useController,
  UseControllerProps,
  useFormContext,
} from "react-hook-form";
import { Modal, Platform, TouchableOpacity } from "react-native";
import { AppText, AppView } from "../common";

type FormDatePickerProps<T extends FieldValues> = UseControllerProps<T> & {
  label?: string;
  placeholder?: string;
  defaultDate?: Date;
  mode?: "date" | "time" | "datetime";
  containerClass?: string;
  labelClass?: string;
  pickerClass?: string;
  errorClass?: string;
  dateFormat?: (date: Date) => string;
};

export default function FormDatePicker<T extends FieldValues>({
  name,
  label,
  placeholder = "Select date",
  defaultDate,
  mode = "date",
  control,
  containerClass,
  labelClass,
  pickerClass,
  errorClass,
  dateFormat,
}: FormDatePickerProps<T>) {
  const [isPickerVisible, setPickerVisible] = useState(false);
  const [pickerMode, setPickerMode] = useState<"date" | "time">(
    mode === "time" ? "time" : "date"
  );
  const form = useFormContext();
  const { field, fieldState } = useController({
    control: control ?? form.control,
    name,
  });

  // Set default date if provided and no value is already set
  useEffect(() => {
    if (defaultDate && !field.value) {
      field.onChange(defaultDate);
    }
  }, [defaultDate, field]);

  const handleChange = (event: any, selectedDate?: Date) => {
    if (Platform.OS === "android") {
      setPickerVisible(false);
    }

    if (selectedDate) {
      // For datetime mode on Android, we need to handle date and time separately
      if (mode === "datetime" && Platform.OS === "android") {
        if (pickerMode === "date") {
          // After selecting date, show time picker
          const currentDate = field.value || new Date();
          const newDate = new Date(selectedDate);
          // Preserve the time from the current value
          newDate.setHours(currentDate.getHours());
          newDate.setMinutes(currentDate.getMinutes());

          field.onChange(newDate);
          setPickerMode("time");
          setPickerVisible(true);
        } else {
          // After selecting time, we're done
          const currentDate = field.value || new Date();
          const newDate = new Date(currentDate);
          // Update only the time part
          newDate.setHours(selectedDate.getHours());
          newDate.setMinutes(selectedDate.getMinutes());

          field.onChange(newDate);
        }
      } else {
        // For single mode (date or time), just update the value
        // Create a new date to avoid timezone issues
        const newDate = new Date(selectedDate);

        // If it's a time picker, ensure we're only changing the time
        if (mode === "time" && field.value) {
          const currentDate = new Date(field.value);
          currentDate.setHours(newDate.getHours());
          currentDate.setMinutes(newDate.getMinutes());
          field.onChange(currentDate);
        } else {
          field.onChange(newDate);
        }
      }
    }
  };

  const handleWebInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.value) {
      let newDate: Date;

      if (mode === "time") {
        // For time input, we need to preserve the date
        newDate = field.value ? new Date(field.value) : new Date();
        const [hours, minutes] = e.target.value.split(":").map(Number);
        newDate.setHours(hours);
        newDate.setMinutes(minutes);
      } else if (mode === "date") {
        // For date input, we need to preserve the time if there was a previous value
        newDate = new Date(e.target.value);
        if (field.value) {
          const currentDate = new Date(field.value);
          newDate.setHours(currentDate.getHours());
          newDate.setMinutes(currentDate.getMinutes());
        }
      } else {
        // For datetime-local input, we get both date and time
        newDate = new Date(e.target.value);
      }

      field.onChange(newDate);
    }
  };

  const formatDate = (date: Date | null): string => {
    if (!date) return placeholder;

    if (dateFormat) {
      return dateFormat(date);
    }

    const options: Intl.DateTimeFormatOptions = {};

    if (mode === "date" || mode === "datetime") {
      options.year = "numeric";
      options.month = "short";
      options.day = "numeric";
    }

    if (mode === "time" || mode === "datetime") {
      options.hour = "2-digit";
      options.minute = "2-digit";
    }

    return date.toLocaleString(undefined, options);
  };

  // Format date for HTML input value
  const formatDateForInput = (date: Date | null): string => {
    if (!date) return "";

    if (mode === "time") {
      return `${String(date.getHours()).padStart(2, "0")}:${String(
        date.getMinutes()
      ).padStart(2, "0")}`;
    } else if (mode === "date") {
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
        2,
        "0"
      )}-${String(date.getDate()).padStart(2, "0")}`;
    } else {
      // datetime-local requires a specific format: YYYY-MM-DDThh:mm
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
        2,
        "0"
      )}-${String(date.getDate()).padStart(2, "0")}T${String(
        date.getHours()
      ).padStart(2, "0")}:${String(date.getMinutes()).padStart(2, "0")}`;
    }
  };

  const showPicker = () => {
    // For datetime on Android, always start with date picker
    if (mode === "datetime" && Platform.OS === "android") {
      setPickerMode("date");
    } else {
      setPickerMode(mode === "time" ? "time" : "date");
    }
    setPickerVisible(true);
  };

  const primaryColor = useThemeColor("primary");
  const { currentTheme } = useTheme();
  const isDark = currentTheme === "dark";

  // Render different picker based on platform
  const renderPicker = () => {
    if (!isPickerVisible) return null;

    // For iOS, we can show the picker in a modal
    if (Platform.OS === "ios") {
      return (
        <Modal
          visible={isPickerVisible}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setPickerVisible(false)}
        >
          <TouchableOpacity
            activeOpacity={1}
            onPress={() => setPickerVisible(false)}
            className="flex-1 bg-black/50"
          >
            <AppView className="flex-1 justify-end">
              <TouchableOpacity activeOpacity={1}>
                <AppView variant="card" className="rounded-t-xl">
                  <AppView className="flex-row justify-between items-center p-4 border-b border-gray-200">
                    <AppText variant="h3">{label || "Select Date"}</AppText>
                    <TouchableOpacity
                      onPress={() => setPickerVisible(false)}
                      className="flex-row items-center"
                    >
                      <AppText className="text-gray-400 mr-1">Done</AppText>
                    </TouchableOpacity>
                  </AppView>

                  <AppView className="p-4">
                    <DateTimePicker
                      value={field.value || new Date()}
                      mode={pickerMode}
                      display="spinner"
                      onChange={handleChange}
                      themeVariant={isDark ? "dark" : "light"}
                      accentColor={primaryColor}
                    />
                  </AppView>
                </AppView>
              </TouchableOpacity>
            </AppView>
          </TouchableOpacity>
        </Modal>
      );
    }

    // For Android, we show the native picker directly
    return (
      <DateTimePicker
        value={field.value || new Date()}
        mode={pickerMode}
        is24Hour={true}
        onChange={handleChange}
        display="default"
      />
    );
  };

  const renderWebSelect = () => {
    const inputType =
      mode === "time" ? "time" : mode === "date" ? "date" : "datetime-local";

    return (
      <AppView
        className={cn(
          "relative border border-gray-300 rounded-lg overflow-hidden",
          fieldState.error && "border-red-500",
          pickerClass
        )}
      >
        <input
          type={inputType}
          value={formatDateForInput(field.value)}
          onChange={handleWebInputChange}
          className={`w-full p-3 appearance-none bg-transparent ${
            currentTheme === "dark" ? "text-white" : "text-black"
          }`}
          style={{
            colorScheme: isDark ? "dark" : "light",
            // Make the calendar icon visible in dark mode
            opacity: 1,
          }}
        />
        <AppView className="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
          <Ionicons name="calendar-outline" size={20} color="gray" />
        </AppView>
      </AppView>
    );
  };

  return (
    <AppView className={cn("mb-4", containerClass)}>
      {label && (
        <AppText variant="label" className={cn("mb-1", labelClass)}>
          {label}
        </AppText>
      )}

      {Platform.OS === "web" ? (
        renderWebSelect()
      ) : (
        <>
          <TouchableOpacity
            onPress={showPicker}
            className={cn(
              "flex-row justify-between items-center p-3 border border-gray-300 rounded-lg",
              fieldState.error && "border-red-500",
              pickerClass
            )}
          >
            <AppText className={cn(!field.value && "text-gray-500")}>
              {field.value ? formatDate(field.value) : placeholder}
            </AppText>
            <Ionicons name="calendar-outline" size={20} color="gray" />
          </TouchableOpacity>

          {renderPicker()}
        </>
      )}

      {fieldState.error && (
        <AppText className={cn("text-red-500 text-sm mt-1", errorClass)}>
          {fieldState.error.message}
        </AppText>
      )}
    </AppView>
  );
}

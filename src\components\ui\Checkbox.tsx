import { AntDesign } from '@expo/vector-icons';
import React from 'react';
import { TouchableOpacity, View } from 'react-native';
import { useTheme } from '@/context/ThemeContext';
import { cn } from '@/lib/utils';

type CheckboxProps = {
  id: string;
  checked: boolean;
  onChange: (checked: boolean) => void;
  disabled?: boolean;
  className?: string;
};

export function Checkbox({
  id,
  checked,
  onChange,
  disabled = false,
  className = '',
}: CheckboxProps) {
  const { currentTheme } = useTheme();
  const isDark = currentTheme === 'dark';

  return (
    <TouchableOpacity
      activeOpacity={0.7}
      onPress={() => !disabled && onChange(!checked)}
      className={cn(className)}
      disabled={disabled}
    >
      <View
        className={cn(
          'h-5 w-5 rounded border-2 items-center justify-center',
          checked
            ? 'bg-primary border-primary'
            : isDark
            ? 'border-gray-600 bg-gray-800'
            : 'border-gray-300 bg-white',
          disabled && 'opacity-50'
        )}
      >
        {checked && (
          <AntDesign
            name="check"
            size={14}
            color={isDark ? '#ffffff' : '#ffffff'}
          />
        )}
      </View>
    </TouchableOpacity>
  );
}

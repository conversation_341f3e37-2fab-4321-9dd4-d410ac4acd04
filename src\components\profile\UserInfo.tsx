import { AppT<PERSON><PERSON>, A<PERSON>Vie<PERSON>, Avatar } from "@/components/common";
import React from "react";
import { TouchableOpacity } from "react-native";
import { MaterialIcons } from "@expo/vector-icons";
import { useThemeColor } from "@/hooks/useThemeColor";

function UserInfo() {
  const primaryColor = useThemeColor("primary");
  return (
    <AppView className="flex flex-row items-center space-x-4 m-2">
      <Avatar
        firstName="Sarah"
        lastName="Chen"
        imageUrl="https://lh3.googleusercontent.com/aida-public/AB6AXuCYM60uNAvqHEYLyombE6nMnMb5IhkngmQ_H_w-FEnXAuMMP9lqjdxvJHfI00yZpFr-YkSphI2pGByYwe1dQbnYBi8q_6PIRo3LB14XDhgLoG9P-qIs-sMfa79K86ciCH5jlbbBuzxLUZWyOxJBazMFQEqQdA8pvZ-QZ6VJeq3SjehZZIN8VW28BX_3FyDclTbv7nhYM_KaUFCjQlOrK-pLCl23TcJsgl1tjOZJxuCR9XX1_W_k7meVGpl-66YcEOYp9CaCT_tYrq4"
      />
      <AppView className="flex-1 ml-2">
        <AppText className="text-lg font-semibold">Sarah Chen</AppText>
        <AppText className="text-sm text-gray-500">
          <EMAIL>
        </AppText>
      </AppView>
      <TouchableOpacity className="ml-auto">
        <MaterialIcons name="edit" size={24} color={primaryColor} />
      </TouchableOpacity>
    </AppView>
  );
}

export default UserInfo;

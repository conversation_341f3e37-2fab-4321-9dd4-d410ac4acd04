import { Model, tableSchema } from "@nozbe/watermelondb";
import { field, text } from "@nozbe/watermelondb/decorators";

export default class Post extends Model {
  static table = "posts";

  @text("title") title: string;
  @text("subtitle") subtitle?: string;
  @field("body") body: string;
  @field("is_pinned") isPinned: boolean;
}

export const postSchema = tableSchema({
  name: "posts",
  columns: [
    { name: "title", type: "string" },
    { name: "subtitle", type: "string", isOptional: true },
    { name: "body", type: "string" },
    { name: "is_pinned", type: "boolean" },
  ],
});

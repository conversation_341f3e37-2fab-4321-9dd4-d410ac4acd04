import { useTheme } from '@/context/ThemeContext';
import { cn } from '@/lib/utils';
import Constants from 'expo-constants';
import React from 'react';
import {
  Platform,
  SafeAreaView as RNSafeAreaView,
  StatusBar,
} from 'react-native';

type AppSafeAreaViewProps = React.ComponentProps<typeof RNSafeAreaView> & {
  className?: string;
  children: React.ReactNode;
};

export default function AppSafeAreaView({
  children,
  className = '',
  ...props
}: AppSafeAreaViewProps) {
  const { currentTheme } = useTheme();
  const isDark = currentTheme === 'dark';

  // Get status bar height from Constants
  const statusBarHeight = Constants.statusBarHeight;

  return (
    <RNSafeAreaView
      className={cn('flex-1', isDark ? 'bg-gray-900' : 'bg-gray-50', className)}
      style={{
        paddingTop: Platform.OS === 'android' ? statusBarHeight : 0,
      }}
      {...props}
    >
      <StatusBar
        barStyle={isDark ? 'light-content' : 'dark-content'}
        backgroundColor="transparent"
        translucent={true}
      />
      {children}
    </RNSafeAreaView>
  );
}

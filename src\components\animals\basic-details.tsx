import React from "react";
import { AppText, AppView } from "../common";
import { Image, TouchableOpacity } from "react-native";

export default function BasicDetails() {
  return (
    <AppView variant="borderdCard" className=" rounded-xl">
      <AppView className="flex-row items-center">
        <Image
          source={{
            uri: "https://lh3.googleusercontent.com/aida-public/AB6AXuBMmrM4FjZTb_87F_4o_uQISMiTKyOWThEWjcm7BQiRBqLCge8Vmf25foSx5h7fojnZR7oU9Dpf8dTr_lTHCpv3Q2S7v8tHg1CJxW9gIBGQ5arG_IznkTLtYLmdHGQola7q-nfHXjYdlvHDXOVT2JtunpVSddo_X3Zo0RAg77Vpxdq2UA6GygjaDDlCgaRWwGlgiCM8t6XARLof6oRiV3M8y-TWeBI04kxxN5hzjXSsPwtFcZi-D_yTh76L9M51ttSDvqwYUfhvRE0",
          }}
          className="w-1/3 h-full  mr-4 "
        />
        <AppView className="flex-1 mb-2 mr-4 py-4">
          <AppText variant="h2">Bella</AppText>
          <AppText className="text-gray-600">ID: G-0012</AppText>
          <AppText className="text-gray-600">Goat, 2 years</AppText>
          <AppView className="mt-2 flex-row items-center justify-between">
            <AppText className="bg-green-200 text-green-700 text-xs font-semibold px-3 py-1 rounded-full">
              Healthy
            </AppText>
            <TouchableOpacity>
              <AppText className="text-primary border border-primary hover:bg-primary hover:text-white text-sm px-3 rounded-lg">
                Edit Profile
              </AppText>
            </TouchableOpacity>
          </AppView>
        </AppView>
      </AppView>
    </AppView>
  );
}

import { AppText, AppView } from "@/components/common";
import { Link, Stack } from "expo-router";
import { StyleSheet } from "react-native";

export default function NotFoundScreen() {
  return (
    <>
      <Stack.Screen options={{ title: "Oops!" }} />
      <AppView style={styles.container}>
        <AppText>This screen doesn't exist.</AppText>
        <Link href="/" style={styles.link}>
          <AppText>Go to home screen!</AppText>
        </Link>
      </AppView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    padding: 20,
  },
  link: {
    marginTop: 15,
    paddingVertical: 15,
  },
});
